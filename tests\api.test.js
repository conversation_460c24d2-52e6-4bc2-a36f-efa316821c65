import { jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { createWebAPI } from '../src/web-api/routes.js';
import ChatbotEngine from '../src/core/ChatbotEngine.js';

describe('Web API', () => {
  let app;
  let chatbot;

  const testConfig = {
    personalities: {
      friendly: {
        name: 'Friendly',
        description: 'A friendly personality',
        traits: ['warm', 'helpful'],
        systemPrompt: 'You are friendly'
      },
      professional: {
        name: 'Professional',
        description: 'A professional personality',
        traits: ['formal', 'precise'],
        systemPrompt: 'You are professional'
      }
    },
    defaultPersonality: 'friendly',
    contextWindow: 5,
    commands: {
      enabled: true,
      prefix: '/',
      availableCommands: ['help', 'status', 'set_personality']
    }
  };

  beforeEach(() => {
    chatbot = new ChatbotEngine(testConfig.chatbot || {});
    app = express();
    app.use(express.json());
    app.use('/api', createWebAPI(chatbot, testConfig));
  });

  afterEach(() => {
    if (chatbot) {
      chatbot.sessions.clear();
    }
  });

  describe('POST /api/chat', () => {
    test('should process chat message', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello there!',
          sessionId: 'test-session-1'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.response).toBeDefined();
      expect(response.body.sessionId).toBe('test-session-1');
    });

    test('should generate session ID if not provided', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello there!'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.sessionId).toBeDefined();
      expect(response.body.sessionId).toMatch(/^[a-f0-9-]+$/);
    });

    test('should reject empty message', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          sessionId: 'test-session-2'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBeDefined();
    });

    test('should handle command messages', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: '/help',
          sessionId: 'test-session-3'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.isCommand).toBe(true);
    });

    test('should apply user settings', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello',
          sessionId: 'test-session-4',
          userSettings: {
            personality: 'professional'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.personality).toBe('professional');
    });
  });

  describe('GET /api/personalities', () => {
    test('should return available personalities', async () => {
      const response = await request(app)
        .get('/api/personalities');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.personalities).toBeInstanceOf(Array);
      expect(response.body.personalities.length).toBeGreaterThan(0);
      expect(response.body.default).toBeDefined();
    });

    test('should include personality details', async () => {
      const response = await request(app)
        .get('/api/personalities');

      const personality = response.body.personalities[0];
      expect(personality.id).toBeDefined();
      expect(personality.name).toBeDefined();
      expect(personality.description).toBeDefined();
      expect(personality.traits).toBeInstanceOf(Array);
    });
  });

  describe('POST /api/personality', () => {
    test('should set personality for session', async () => {
      const response = await request(app)
        .post('/api/personality')
        .send({
          sessionId: 'test-session-5',
          personality: 'professional'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.personality.name).toBe('Professional');
    });

    test('should reject invalid personality', async () => {
      const response = await request(app)
        .post('/api/personality')
        .send({
          sessionId: 'test-session-6',
          personality: 'invalid'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBeDefined();
    });

    test('should require session ID and personality', async () => {
      const response = await request(app)
        .post('/api/personality')
        .send({
          sessionId: 'test-session-7'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('GET /api/session/:sessionId', () => {
    test('should return session information', async () => {
      // First create a session by sending a message
      await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello',
          sessionId: 'test-session-8'
        });

      const response = await request(app)
        .get('/api/session/test-session-8');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.session.id).toBe('test-session-8');
      expect(response.body.session.personality).toBeDefined();
      expect(response.body.session.contextLength).toBeGreaterThan(0);
    });

    test('should return 404 for non-existent session', async () => {
      const response = await request(app)
        .get('/api/session/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('DELETE /api/session/:sessionId', () => {
    test('should delete existing session', async () => {
      // Create a session first
      await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello',
          sessionId: 'test-session-9'
        });

      const response = await request(app)
        .delete('/api/session/test-session-9');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify session is deleted
      const getResponse = await request(app)
        .get('/api/session/test-session-9');
      expect(getResponse.status).toBe(404);
    });

    test('should return 404 for non-existent session', async () => {
      const response = await request(app)
        .delete('/api/session/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('POST /api/memory', () => {
    test('should add memory entry', async () => {
      const response = await request(app)
        .post('/api/memory')
        .send({
          content: 'User likes pizza'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('successfully');
    });

    test('should reject empty content', async () => {
      const response = await request(app)
        .post('/api/memory')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('GET /api/config', () => {
    test('should return public configuration', async () => {
      const response = await request(app)
        .get('/api/config');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.config.personalities).toBeDefined();
      expect(response.body.config.defaultPersonality).toBeDefined();
    });
  });

  describe('GET /api/stats', () => {
    test('should return system statistics', async () => {
      const response = await request(app)
        .get('/api/stats');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.stats.activeSessions).toBeDefined();
      expect(response.body.stats.uptime).toBeDefined();
      expect(response.body.stats.timestamp).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send('invalid json')
        .set('Content-Type', 'application/json');

      expect(response.status).toBe(400);
    });

    test('should handle missing content-type', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send('message=hello');

      expect(response.status).toBe(200); // Should still work with form data
    });
  });
});
