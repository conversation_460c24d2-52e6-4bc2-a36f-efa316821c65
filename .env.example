# Discord Bot Configuration
DISCORD_TOKEN=MTMwMTU5MjgzNzQ3Njg0MzYzMQ.GiK3rr.TuJ7pVpEa5dZg8bvNdA7zZL9_0WTRWTvrFk1S0
DISCORD_CLIENT_ID=1301592837476843631

# Web Server Configuration
PORT=3000
NODE_ENV=development

# AI/LLM Configuration
# GitHub Models API (Primary AI Provider)
GITHUB_TOKEN=your_github_personal_access_token_here
GITHUB_API_URL=https://models.inference.ai.azure.com
AI_MODEL=gpt-4o
AI_PROVIDER=github

# Alternative AI Providers (Optional)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# AI Configuration
MAX_TOKENS=1000
TEMPERATURE=0.7
AI_TIMEOUT=30000

# Database Configuration (optional)
DATABASE_URL=sqlite:./data/kora.db

# Security
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Logging
LOG_LEVEL=info
