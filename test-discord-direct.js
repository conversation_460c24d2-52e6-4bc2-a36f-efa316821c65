import DiscordBot from './src/discord/DiscordBot.js';
import ConfigManager from './src/utils/ConfigManager.js';
import fs from 'fs-extra';

async function testDiscordDirect() {
  console.log('🧪 Teste direto do DiscordBot...');
  
  try {
    // Load configuration
    const config = ConfigManager.getAll();
    console.log('✅ Configuração carregada');
    console.log('Discord habilitado:', config.discord?.enabled);
    console.log('Token presente:', !!process.env.DISCORD_TOKEN);
    
    // Create Discord bot instance
    const discordBot = new DiscordBot(config);
    console.log('✅ DiscordBot criado');
    
    // Start the bot
    console.log('🔄 Iniciando bot...');
    await discordBot.start();
    console.log('✅ Bot iniciado com sucesso');
    
    // Log status
    const status = discordBot.getStatus();
    console.log('📊 Status do bot:', status);
    
    // Create a log file to track messages
    const logFile = './discord-test.log';
    await fs.writeFile(logFile, `Bot iniciado em ${new Date().toISOString()}\n`);
    
    // Override handleMessage to log everything
    const originalHandleMessage = discordBot.handleMessage.bind(discordBot);
    discordBot.handleMessage = async function(message) {
      const logEntry = `${new Date().toISOString()} - Mensagem: "${message.content}" de ${message.author.username} (canal: ${message.channel.type})\n`;
      await fs.appendFile(logFile, logEntry);
      console.log('📝 Mensagem logada:', logEntry.trim());
      
      return originalHandleMessage(message);
    };
    
    console.log('🎯 Bot pronto! Envie uma mensagem no Discord...');
    console.log('📝 Logs serão salvos em:', logFile);
    
    // Keep alive for 5 minutes
    setTimeout(() => {
      console.log('⏰ Teste finalizado');
      discordBot.stop();
      process.exit(0);
    }, 300000);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
    process.exit(1);
  }
}

testDiscordDirect();
