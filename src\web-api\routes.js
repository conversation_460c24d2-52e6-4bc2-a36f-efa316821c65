import express from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * Create Web API routes for the chatbot
 */
export function createWebAPI(chatbot, config) {
  const router = express.Router();

  /**
   * POST /api/chat - Send a message to the chatbot
   */
  router.post('/chat', async (req, res) => {
    try {
      const { message, sessionId, userSettings = {} } = req.body;

      if (!message || typeof message !== 'string') {
        return res.status(400).json({
          error: 'Message is required and must be a string'
        });
      }

      // Generate session ID if not provided
      const finalSessionId = sessionId || uuidv4();

      // Process the message
      const response = await chatbot.processMessage(
        message,
        finalSessionId,
        'web',
        userSettings
      );

      res.json({
        success: true,
        response: response.content,
        sessionId: finalSessionId,
        personality: response.personality,
        isCommand: response.isCommand || false,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Chat API error:', error);
      res.status(500).json({
        error: 'Failed to process message',
        message: error.message
      });
    }
  });

  /**
   * GET /api/personalities - Get available personalities
   */
  router.get('/personalities', (req, res) => {
    try {
      const personalities = Object.entries(config.personalities || {}).map(([key, personality]) => ({
        id: key,
        name: personality.name,
        description: personality.description,
        traits: personality.traits
      }));

      res.json({
        success: true,
        personalities,
        default: config.defaultPersonality || 'friendly'
      });
    } catch (error) {
      console.error('Personalities API error:', error);
      res.status(500).json({
        error: 'Failed to get personalities'
      });
    }
  });

  /**
   * POST /api/personality - Set personality for a session
   */
  router.post('/personality', async (req, res) => {
    try {
      const { sessionId, personality } = req.body;

      if (!sessionId || !personality) {
        return res.status(400).json({
          error: 'Session ID and personality are required'
        });
      }

      if (!config.personalities || !config.personalities[personality]) {
        return res.status(400).json({
          error: 'Invalid personality',
          available: Object.keys(config.personalities || {})
        });
      }

      // Get or create session and update personality
      const session = chatbot.getOrCreateSession(sessionId);
      session.personality = personality;

      res.json({
        success: true,
        personality: config.personalities[personality],
        sessionId
      });

    } catch (error) {
      console.error('Set personality API error:', error);
      res.status(500).json({
        error: 'Failed to set personality'
      });
    }
  });

  /**
   * GET /api/session/:sessionId - Get session information
   */
  router.get('/session/:sessionId', (req, res) => {
    try {
      const { sessionId } = req.params;
      const session = chatbot.getSessionInfo(sessionId);

      if (!session) {
        return res.status(404).json({
          error: 'Session not found'
        });
      }

      // Return session info without sensitive data
      res.json({
        success: true,
        session: {
          id: session.id,
          personality: session.personality,
          contextLength: session.context.length,
          memoryEntries: session.memory.length,
          loreEntries: session.lore.length,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity
        }
      });

    } catch (error) {
      console.error('Session API error:', error);
      res.status(500).json({
        error: 'Failed to get session information'
      });
    }
  });

  /**
   * DELETE /api/session/:sessionId - Delete a session
   */
  router.delete('/session/:sessionId', (req, res) => {
    try {
      const { sessionId } = req.params;
      
      if (chatbot.sessions.has(sessionId)) {
        chatbot.sessions.delete(sessionId);
        res.json({
          success: true,
          message: 'Session deleted'
        });
      } else {
        res.status(404).json({
          error: 'Session not found'
        });
      }

    } catch (error) {
      console.error('Delete session API error:', error);
      res.status(500).json({
        error: 'Failed to delete session'
      });
    }
  });

  /**
   * POST /api/memory - Add a memory entry
   */
  router.post('/memory', async (req, res) => {
    try {
      const { content } = req.body;

      if (!content || typeof content !== 'string') {
        return res.status(400).json({
          error: 'Memory content is required and must be a string'
        });
      }

      chatbot.addMemory(content);

      res.json({
        success: true,
        message: 'Memory added successfully'
      });

    } catch (error) {
      console.error('Add memory API error:', error);
      res.status(500).json({
        error: 'Failed to add memory'
      });
    }
  });

  /**
   * POST /api/lore - Load lore data for a session
   */
  router.post('/lore', async (req, res) => {
    try {
      const { sessionId, loreData } = req.body;

      if (!sessionId || !loreData) {
        return res.status(400).json({
          error: 'Session ID and lore data are required'
        });
      }

      const session = chatbot.getOrCreateSession(sessionId);
      
      // Merge lore data
      if (Array.isArray(loreData)) {
        session.lore = [...session.lore, ...loreData];
      } else if (typeof loreData === 'object') {
        session.lore.push(loreData);
      }

      res.json({
        success: true,
        message: 'Lore data loaded successfully',
        loreEntries: session.lore.length
      });

    } catch (error) {
      console.error('Load lore API error:', error);
      res.status(500).json({
        error: 'Failed to load lore data'
      });
    }
  });

  /**
   * GET /api/config - Get public configuration
   */
  router.get('/config', (req, res) => {
    try {
      res.json({
        success: true,
        config: {
          personalities: config.personalities || {},
          defaultPersonality: config.defaultPersonality || 'friendly',
          contextWindow: config.contextWindow || 10,
          commands: config.commands || {},
          platforms: {
            web: config.web?.enabled || true,
            discord: config.discord?.enabled || false
          }
        }
      });
    } catch (error) {
      console.error('Config API error:', error);
      res.status(500).json({
        error: 'Failed to get configuration'
      });
    }
  });

  /**
   * GET /api/stats - Get chatbot statistics
   */
  router.get('/stats', (req, res) => {
    try {
      res.json({
        success: true,
        stats: {
          activeSessions: chatbot.sessions.size,
          totalMemoryEntries: chatbot.memoryManager?.memory?.length || 0,
          availablePersonalities: Object.keys(config.personalities || {}).length,
          uptime: process.uptime(),
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Stats API error:', error);
      res.status(500).json({
        error: 'Failed to get statistics'
      });
    }
  });

  return router;
}
