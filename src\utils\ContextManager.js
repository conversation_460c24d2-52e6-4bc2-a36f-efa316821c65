import { v4 as uuidv4 } from 'uuid';

/**
 * Context Manager - Handles conversation context and flow
 */
class ContextManager {
  constructor(options = {}) {
    this.maxContextLength = options.maxContextLength || 10;
    this.maxTokens = options.maxTokens || 4000; // Approximate token limit
    this.compressionThreshold = options.compressionThreshold || 20;
    this.importantKeywords = options.importantKeywords || [
      'remember', 'important', 'never forget', 'always', 'my name is',
      'i am', 'i work', 'i live', 'my favorite', 'i love', 'i hate'
    ];
  }

  /**
   * Process and manage conversation context
   */
  processContext(session, newMessage = null) {
    if (newMessage) {
      this.addMessageToContext(session, newMessage);
    }

    // Trim context if it's getting too long
    if (session.context.length > this.maxContextLength * 2) {
      this.trimContext(session);
    }

    // Compress old context if needed
    if (session.context.length > this.compressionThreshold) {
      this.compressOldContext(session);
    }

    return this.buildContextString(session);
  }

  /**
   * Add a message to the session context
   */
  addMessageToContext(session, message) {
    const contextEntry = {
      id: uuidv4(),
      role: message.role || 'user',
      content: message.content,
      timestamp: new Date(),
      platform: message.platform || 'web',
      metadata: message.metadata || {}
    };

    // Check if message contains important information
    if (this.containsImportantInfo(message.content)) {
      contextEntry.important = true;
      contextEntry.metadata.importance = 'high';
    }

    session.context.push(contextEntry);
    session.lastActivity = new Date();
  }

  /**
   * Check if message contains important information to remember
   */
  containsImportantInfo(content) {
    const contentLower = content.toLowerCase();
    return this.importantKeywords.some(keyword => 
      contentLower.includes(keyword.toLowerCase())
    );
  }

  /**
   * Trim context to keep within limits
   */
  trimContext(session) {
    // Always keep important messages
    const importantMessages = session.context.filter(msg => msg.important);
    const recentMessages = session.context.slice(-this.maxContextLength);
    
    // Combine important and recent messages, removing duplicates
    const contextMap = new Map();
    
    [...importantMessages, ...recentMessages].forEach(msg => {
      contextMap.set(msg.id, msg);
    });
    
    session.context = Array.from(contextMap.values())
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  }

  /**
   * Compress old context into summaries
   */
  compressOldContext(session) {
    if (session.context.length <= this.compressionThreshold) {
      return;
    }

    const cutoffIndex = Math.floor(this.compressionThreshold / 2);
    const oldMessages = session.context.slice(0, cutoffIndex);
    const recentMessages = session.context.slice(cutoffIndex);

    // Create a summary of old messages
    const summary = this.createContextSummary(oldMessages);
    
    // Create a summary entry
    const summaryEntry = {
      id: uuidv4(),
      role: 'system',
      content: `[Context Summary: ${summary}]`,
      timestamp: new Date(),
      platform: 'system',
      metadata: { type: 'summary', originalCount: oldMessages.length },
      compressed: true
    };

    // Replace old messages with summary
    session.context = [summaryEntry, ...recentMessages];
  }

  /**
   * Create a summary of conversation context
   */
  createContextSummary(messages) {
    const topics = new Set();
    const userInfo = [];
    const keyPoints = [];

    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      
      // Extract user information
      if (content.includes('my name is') || content.includes('i am')) {
        userInfo.push(msg.content);
      }
      
      // Extract important statements
      if (msg.important || this.containsImportantInfo(msg.content)) {
        keyPoints.push(msg.content);
      }
      
      // Extract topics (simple keyword extraction)
      const words = content.split(' ').filter(word => word.length > 4);
      words.forEach(word => topics.add(word));
    });

    const summaryParts = [];
    
    if (userInfo.length > 0) {
      summaryParts.push(`User info: ${userInfo.join(', ')}`);
    }
    
    if (keyPoints.length > 0) {
      summaryParts.push(`Key points: ${keyPoints.slice(0, 3).join('; ')}`);
    }
    
    if (topics.size > 0) {
      const topicList = Array.from(topics).slice(0, 5).join(', ');
      summaryParts.push(`Topics discussed: ${topicList}`);
    }

    return summaryParts.join(' | ') || 'General conversation';
  }

  /**
   * Build context string for AI processing
   */
  buildContextString(session) {
    const contextMessages = session.context.slice(-this.maxContextLength);
    
    return contextMessages.map(msg => {
      const role = msg.role === 'user' ? 'Human' : 'Assistant';
      const timestamp = msg.timestamp.toLocaleTimeString();
      return `[${timestamp}] ${role}: ${msg.content}`;
    }).join('\n');
  }

  /**
   * Get relevant context based on current message
   */
  getRelevantContext(session, currentMessage, memoryManager = null) {
    const context = {
      recent: this.getRecentContext(session),
      relevant: [],
      memory: [],
      lore: []
    };

    // Get relevant memory if memory manager is available
    if (memoryManager) {
      context.memory = memoryManager.searchMemory(currentMessage, 5);
      context.lore = memoryManager.searchLore(currentMessage);
    }

    // Find relevant previous messages
    const currentLower = currentMessage.toLowerCase();
    context.relevant = session.context.filter(msg => {
      const msgLower = msg.content.toLowerCase();
      return this.calculateRelevance(currentLower, msgLower) > 0.3;
    }).slice(0, 3);

    return context;
  }

  /**
   * Calculate relevance between two messages (simple implementation)
   */
  calculateRelevance(message1, message2) {
    const words1 = new Set(message1.split(' ').filter(w => w.length > 3));
    const words2 = new Set(message2.split(' ').filter(w => w.length > 3));
    
    const intersection = new Set([...words1].filter(w => words2.has(w)));
    const union = new Set([...words1, ...words2]);
    
    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * Get recent context messages
   */
  getRecentContext(session, limit = 5) {
    return session.context.slice(-limit);
  }

  /**
   * Extract entities from context (simple implementation)
   */
  extractEntities(session) {
    const entities = {
      people: new Set(),
      places: new Set(),
      topics: new Set(),
      dates: new Set()
    };

    session.context.forEach(msg => {
      const content = msg.content;
      
      // Simple entity extraction patterns
      const peoplePattern = /(?:my name is|i am|call me) (\w+)/gi;
      const placePattern = /(?:in|at|from) ([A-Z][a-z]+ ?[A-Z]?[a-z]*)/g;
      const datePattern = /\b\d{1,2}\/\d{1,2}\/\d{2,4}\b|\b\d{4}-\d{2}-\d{2}\b/g;
      
      let match;
      
      while ((match = peoplePattern.exec(content)) !== null) {
        entities.people.add(match[1]);
      }
      
      while ((match = placePattern.exec(content)) !== null) {
        entities.places.add(match[1]);
      }
      
      while ((match = datePattern.exec(content)) !== null) {
        entities.dates.add(match[0]);
      }
    });

    // Convert sets to arrays
    return {
      people: Array.from(entities.people),
      places: Array.from(entities.places),
      topics: Array.from(entities.topics),
      dates: Array.from(entities.dates)
    };
  }

  /**
   * Analyze conversation sentiment and mood
   */
  analyzeMood(session, recentLimit = 5) {
    const recentMessages = session.context.slice(-recentLimit);
    
    let positiveScore = 0;
    let negativeScore = 0;
    let neutralScore = 0;

    const positiveWords = [
      'happy', 'good', 'great', 'awesome', 'love', 'excited', 'wonderful',
      'amazing', 'fantastic', 'excellent', 'perfect', 'brilliant'
    ];
    
    const negativeWords = [
      'sad', 'bad', 'terrible', 'hate', 'angry', 'frustrated', 'awful',
      'horrible', 'disappointed', 'upset', 'annoyed', 'worried'
    ];

    recentMessages.forEach(msg => {
      if (msg.role === 'user') {
        const words = msg.content.toLowerCase().split(' ');
        
        words.forEach(word => {
          if (positiveWords.includes(word)) positiveScore++;
          if (negativeWords.includes(word)) negativeScore++;
        });
        
        if (positiveScore === 0 && negativeScore === 0) {
          neutralScore++;
        }
      }
    });

    const total = positiveScore + negativeScore + neutralScore;
    
    if (total === 0) {
      return { mood: 'neutral', confidence: 0 };
    }

    if (positiveScore > negativeScore) {
      return { 
        mood: 'positive', 
        confidence: positiveScore / total,
        details: { positive: positiveScore, negative: negativeScore, neutral: neutralScore }
      };
    } else if (negativeScore > positiveScore) {
      return { 
        mood: 'negative', 
        confidence: negativeScore / total,
        details: { positive: positiveScore, negative: negativeScore, neutral: neutralScore }
      };
    } else {
      return { 
        mood: 'neutral', 
        confidence: neutralScore / total,
        details: { positive: positiveScore, negative: negativeScore, neutral: neutralScore }
      };
    }
  }

  /**
   * Get context statistics
   */
  getContextStats(session) {
    const stats = {
      totalMessages: session.context.length,
      userMessages: session.context.filter(msg => msg.role === 'user').length,
      botMessages: session.context.filter(msg => msg.role === 'assistant').length,
      importantMessages: session.context.filter(msg => msg.important).length,
      compressedEntries: session.context.filter(msg => msg.compressed).length,
      platforms: {}
    };

    // Count messages by platform
    session.context.forEach(msg => {
      stats.platforms[msg.platform] = (stats.platforms[msg.platform] || 0) + 1;
    });

    // Calculate conversation duration
    if (session.context.length > 0) {
      const firstMessage = session.context[0];
      const lastMessage = session.context[session.context.length - 1];
      stats.duration = new Date(lastMessage.timestamp) - new Date(firstMessage.timestamp);
    }

    return stats;
  }
}

export default ContextManager;
