{"server": {"port": 3000, "host": "0.0.0.0"}, "discord": {"enabled": true, "commandPrefix": "/", "maxMessageLength": 1500, "activityType": "LISTENING", "activityName": "conversations with humans"}, "web": {"enabled": true, "cors": {"origin": false, "credentials": false}}, "chatbot": {"defaultPersonality": "friendly", "contextWindow": 10, "maxMemoryEntries": 100, "responseTimeout": 30000, "enableLearning": true, "autoSaveInterval": 600000, "ai": {"provider": "github", "model": "gpt-4o", "maxTokens": 800, "temperature": 0.6, "timeout": 25000}}, "personalities": {"friendly": {"name": "Friendly and Helpful", "description": "Warm, engaging, and slightly playful tone", "traits": ["warm", "engaging", "playful", "helpful"], "systemPrompt": "You are a friendly and helpful AI assistant with a warm, engaging personality.", "responseStyle": "casual", "emojiUsage": "moderate"}, "professional": {"name": "Professional", "description": "Formal, structured, and business-oriented", "traits": ["formal", "structured", "precise", "business-oriented"], "systemPrompt": "You are a professional AI assistant focused on providing precise, structured information.", "responseStyle": "formal", "emojiUsage": "minimal"}, "flirty": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Playful, charming, and subtly romantic", "traits": ["playful", "charming", "romantic", "witty"], "systemPrompt": "You are a charming and playful AI with a flirty personality, keeping things fun and engaging.", "responseStyle": "playful", "emojiUsage": "frequent"}, "roleplay": {"name": "Roleplay Character", "description": "Adaptable character for roleplay scenarios", "traits": ["immersive", "adaptable", "creative", "character-driven"], "systemPrompt": "You are a roleplay character. Stay fully in character and maintain immersion.", "responseStyle": "narrative", "emojiUsage": "contextual"}}, "commands": {"enabled": true, "prefix": "/", "availableCommands": ["set_personality", "reset_context", "help", "status"]}, "logging": {"level": "info", "file": "./data/logs/kora.log", "console": false, "maxFiles": 10, "maxSize": "50m"}, "features": {"contextCompression": true, "moodAnalysis": true, "entityExtraction": false, "memorySearch": true, "loreIntegration": true, "autoPersonalityAdjustment": false}}