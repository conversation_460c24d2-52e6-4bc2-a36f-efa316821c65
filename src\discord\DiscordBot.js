import { Client, GatewayIntentBits, Events, ActivityType, ChannelType } from 'discord.js';
import dotenv from 'dotenv';
import fs from 'fs-extra';
import path from 'path';

import ChatbotEngine from '../core/ChatbotEngine.js';
import ConfigManager from '../utils/ConfigManager.js';

dotenv.config();

/**
 * Discord Bot Integration for Kora 2.0
 */
class DiscordBot {
  constructor(config = {}) {
    this.config = config;
    this.chatbot = null;
    this.client = null;
    this.isReady = false;

    // Discord-specific settings
    this.maxMessageLength = config.discord?.maxMessageLength || 1500;
    this.commandPrefix = config.discord?.commandPrefix || '/';
  }

  async initialize() {
    try {
      // Load configuration using ConfigManager
      const config = ConfigManager.getAll();

      // Initialize chatbot engine
      const chatbotConfig = {
        personalities: config.personalities || {},
        defaultPersonality: config.chatbot?.defaultPersonality || config.defaultPersonality || 'friendly',
        contextWindow: config.chatbot?.contextWindow || config.contextWindow || 10,
        maxMemoryEntries: config.chatbot?.maxMemoryEntries || config.maxMemoryEntries || 100,
        commands: config.commands || {},
        ai: config.chatbot?.ai || config.ai || {}
      };

      this.chatbot = new ChatbotEngine(chatbotConfig);

      // Initialize Discord client
      this.client = new Client({
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMessages,
          GatewayIntentBits.MessageContent,
          GatewayIntentBits.DirectMessages,
          GatewayIntentBits.DirectMessageReactions
        ]
      });

      this.setupEventHandlers();

    } catch (error) {
      console.error('Failed to initialize Discord bot:', error);
      throw error;
    }
  }

  setupEventHandlers() {
    // Bot ready event
    this.client.once(Events.ClientReady, (readyClient) => {
      console.log(`🤖 Discord bot logged in as ${readyClient.user.tag}`);
      
      // Set bot status
      this.client.user.setActivity('conversations with humans', { 
        type: ActivityType.Listening 
      });
      
      this.isReady = true;
    });

    // Message handling
    this.client.on(Events.MessageCreate, async (message) => {
      await this.handleMessage(message);
    });

    // Error handling
    this.client.on(Events.Error, (error) => {
      console.error('Discord client error:', error);
    });

    // Disconnect handling
    this.client.on(Events.Disconnect, () => {
      console.log('Discord bot disconnected');
      this.isReady = false;
    });
  }

  async handleMessage(message) {
    try {
      const logMessage = `📨 Discord message received: "${message.content}" from ${message.author.username}`;
      console.log(logMessage);

      // Log to file for debugging
      const logEntry = `${new Date().toISOString()} - ${logMessage}\n`;
      await fs.appendFile('./discord-debug.log', logEntry).catch(() => {});

      console.log(`📍 Channel info: type=${message.channel.type}, id=${message.channel.id}`);

      // Ignore bot messages and system messages
      if (message.author.bot || message.system) {
        console.log('🤖 Ignoring bot/system message');
        return;
      }

      // Check if message is directed at the bot
      const isMentioned = message.mentions.has(this.client.user);
      const isDM = message.channel.type === ChannelType.DM;
      const startsWithPrefix = message.content.startsWith(this.commandPrefix);

      console.log(`🔍 Message check: mentioned=${isMentioned}, DM=${isDM}, prefix=${startsWithPrefix}`);
      console.log(`🔍 Channel type: ${message.channel.type} (DM type: ${ChannelType.DM})`);

      // Log decision to file
      const decisionLog = `${new Date().toISOString()} - Decision: mentioned=${isMentioned}, DM=${isDM}, prefix=${startsWithPrefix}\n`;
      await fs.appendFile('./discord-debug.log', decisionLog).catch(() => {});

      // Only respond if mentioned, in DM, or starts with command prefix
      if (!isMentioned && !isDM && !startsWithPrefix) {
        console.log('❌ Message not directed at bot, ignoring');
        const ignoreLog = `${new Date().toISOString()} - IGNORED: Message not directed at bot\n`;
        await fs.appendFile('./discord-debug.log', ignoreLog).catch(() => {});
        return;
      }

      console.log('✅ Message directed at bot, processing...');
      const processLog = `${new Date().toISOString()} - PROCESSING: Message directed at bot\n`;
      await fs.appendFile('./discord-debug.log', processLog).catch(() => {});

      // Clean the message content
      let cleanContent = message.content;
      
      // Remove bot mention
      if (isMentioned) {
        cleanContent = cleanContent.replace(`<@${this.client.user.id}>`, '').trim();
      }

      // Generate session ID based on channel and user
      const sessionId = `discord_${message.channel.id}_${message.author.id}`;

      // Get user settings (could be expanded to store user preferences)
      const userSettings = {
        username: message.author.username,
        displayName: message.author.displayName || message.author.username,
        platform: 'discord'
      };

      // Show typing indicator
      console.log('⌨️ Showing typing indicator...');
      await message.channel.sendTyping();

      // Process message with chatbot
      console.log(`🤖 Processing with chatbot: "${cleanContent}"`);
      const response = await this.chatbot.processMessage(
        cleanContent,
        sessionId,
        'discord',
        userSettings
      );

      console.log(`✅ Response received: "${response.content.substring(0, 50)}..."`);

      // Send response
      console.log('📤 Sending response...');
      await this.sendResponse(message, response);
      console.log('✅ Response sent successfully');

    } catch (error) {
      console.error('Error handling Discord message:', error);
      
      try {
        await message.reply('Sorry, I encountered an error processing your message. Please try again!');
      } catch (replyError) {
        console.error('Failed to send error message:', replyError);
      }
    }
  }

  async sendResponse(originalMessage, response) {
    try {
      let content = response.content;

      // Handle long messages by splitting them
      if (content.length > this.maxMessageLength) {
        const chunks = this.splitMessage(content, this.maxMessageLength);
        
        for (let i = 0; i < chunks.length; i++) {
          const chunk = chunks[i];
          
          if (i === 0) {
            await originalMessage.reply(chunk);
          } else {
            await originalMessage.channel.send(chunk);
          }
          
          // Small delay between chunks to avoid rate limiting
          if (i < chunks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      } else {
        await originalMessage.reply(content);
      }

    } catch (error) {
      console.error('Error sending Discord response:', error);
      throw error;
    }
  }

  splitMessage(content, maxLength) {
    const chunks = [];
    let currentChunk = '';
    
    // Split by sentences first
    const sentences = content.split(/(?<=[.!?])\s+/);
    
    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length + 1 <= maxLength) {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk);
          currentChunk = sentence;
        } else {
          // Sentence is too long, split by words
          const words = sentence.split(' ');
          for (const word of words) {
            if (currentChunk.length + word.length + 1 <= maxLength) {
              currentChunk += (currentChunk ? ' ' : '') + word;
            } else {
              if (currentChunk) {
                chunks.push(currentChunk);
                currentChunk = word;
              } else {
                // Word is too long, force split
                chunks.push(word.substring(0, maxLength - 3) + '...');
                currentChunk = '...' + word.substring(maxLength - 3);
              }
            }
          }
        }
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk);
    }
    
    return chunks;
  }

  async start() {
    try {
      const token = process.env.DISCORD_TOKEN;

      if (!token) {
        throw new Error('DISCORD_TOKEN environment variable is required');
      }

      // Initialize first
      await this.initialize();

      console.log('🔄 Starting Discord bot...');
      await this.client.login(token);

    } catch (error) {
      console.error('Failed to start Discord bot:', error);
      throw error;
    }
  }

  async stop() {
    try {
      if (this.client) {
        console.log('🔄 Stopping Discord bot...');
        await this.client.destroy();
        this.isReady = false;
        console.log('✅ Discord bot stopped');
      }
    } catch (error) {
      console.error('Error stopping Discord bot:', error);
    }
  }

  getStatus() {
    return {
      ready: this.isReady,
      guilds: this.client?.guilds?.cache?.size || 0,
      users: this.client?.users?.cache?.size || 0,
      uptime: this.client?.uptime || 0
    };
  }

  // Method to send a message to a specific channel (for external use)
  async sendToChannel(channelId, content) {
    try {
      if (!this.isReady) {
        throw new Error('Discord bot is not ready');
      }

      const channel = await this.client.channels.fetch(channelId);
      if (!channel) {
        throw new Error('Channel not found');
      }

      if (content.length > this.maxMessageLength) {
        const chunks = this.splitMessage(content, this.maxMessageLength);
        for (const chunk of chunks) {
          await channel.send(chunk);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } else {
        await channel.send(content);
      }

    } catch (error) {
      console.error('Error sending message to channel:', error);
      throw error;
    }
  }

  // Method to send a DM to a user
  async sendDM(userId, content) {
    try {
      if (!this.isReady) {
        throw new Error('Discord bot is not ready');
      }

      const user = await this.client.users.fetch(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (content.length > this.maxMessageLength) {
        const chunks = this.splitMessage(content, this.maxMessageLength);
        for (const chunk of chunks) {
          await user.send(chunk);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } else {
        await user.send(content);
      }

    } catch (error) {
      console.error('Error sending DM:', error);
      throw error;
    }
  }
}

export default DiscordBot;
