import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

/**
 * AI Service - Handles integration with various AI providers
 * Primary: GitHub Models API with ChatGPT 4.1
 * Fallback: OpenAI, Anthropic
 */
class AIService {
  constructor(config = {}) {
    this.provider = process.env.AI_PROVIDER || config.provider || 'github';
    this.model = process.env.AI_MODEL || config.model || 'gpt-4o';
    this.maxTokens = parseInt(process.env.MAX_TOKENS) || config.maxTokens || 1000;
    this.temperature = parseFloat(process.env.TEMPERATURE) || config.temperature || 0.7;
    this.timeout = parseInt(process.env.AI_TIMEOUT) || config.timeout || 30000;
    
    // GitHub Models API configuration
    this.githubToken = process.env.GITHUB_TOKEN;
    this.githubApiUrl = process.env.GITHUB_API_URL || 'https://models.inference.ai.azure.com';
    
    // Alternative providers
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.anthropicApiKey = process.env.ANTHROPIC_API_KEY;
    
    this.initializeProvider();
  }

  /**
   * Initialize the AI provider
   */
  initializeProvider() {
    switch (this.provider) {
      case 'github':
        if (!this.githubToken) {
          console.warn('GitHub token not provided. AI responses will use rule-based fallback.');
          this.provider = 'fallback';
        }
        break;
      case 'openai':
        if (!this.openaiApiKey) {
          console.warn('OpenAI API key not provided. Switching to GitHub Models or fallback.');
          this.provider = this.githubToken ? 'github' : 'fallback';
        }
        break;
      case 'anthropic':
        if (!this.anthropicApiKey) {
          console.warn('Anthropic API key not provided. Switching to GitHub Models or fallback.');
          this.provider = this.githubToken ? 'github' : 'fallback';
        }
        break;
      default:
        this.provider = 'fallback';
    }
    
    console.log(`AI Service initialized with provider: ${this.provider}, model: ${this.model}`);
  }

  /**
   * Generate AI response using the configured provider
   */
  async generateResponse(messages, personality, context = {}) {
    console.log(`🤖 Generating response with provider: ${this.provider}`);

    try {
      switch (this.provider) {
        case 'github':
          return await this.generateGitHubResponse(messages, personality, context);
        case 'openai':
          return await this.generateOpenAIResponse(messages, personality, context);
        case 'anthropic':
          return await this.generateAnthropicResponse(messages, personality, context);
        default:
          console.log('📝 Using fallback response generation');
          return this.generateFallbackResponse(messages, personality, context);
      }
    } catch (error) {
      console.error(`❌ AI generation error with ${this.provider}:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      // Fallback to rule-based response on error
      console.log('🔄 Falling back to rule-based responses');
      return this.generateFallbackResponse(messages, personality, context);
    }
  }

  /**
   * Generate response using GitHub Models API (ChatGPT 4.1)
   */
  async generateGitHubResponse(messages, personality, context) {
    const systemPrompt = this.buildSystemPrompt(personality, context);

    const requestBody = {
      messages: [
        { role: 'system', content: systemPrompt },
        ...messages.map(msg => ({
          role: msg.role === 'user' ? 'user' : 'assistant',
          content: msg.content
        }))
      ],
      model: this.model,
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      stream: false
    };

    console.log('🔍 GitHub API Request:', {
      url: `${this.githubApiUrl}/chat/completions`,
      model: this.model,
      hasToken: !!this.githubToken,
      tokenPrefix: this.githubToken ? this.githubToken.substring(0, 10) + '...' : 'none'
    });

    const response = await axios.post(
      `${this.githubApiUrl}/chat/completions`,
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${this.githubToken}`,
          'Content-Type': 'application/json'
        },
        timeout: this.timeout
      }
    );

    console.log('✅ GitHub API Response received:', {
      status: response.status,
      hasChoices: !!response.data?.choices,
      choicesLength: response.data?.choices?.length || 0
    });

    if (response.data?.choices?.[0]?.message?.content) {
      return {
        content: response.data.choices[0].message.content.trim(),
        provider: 'github',
        model: this.model,
        usage: response.data.usage
      };
    } else {
      throw new Error('Invalid response format from GitHub Models API');
    }
  }

  /**
   * Generate response using OpenAI API
   */
  async generateOpenAIResponse(messages, personality, context) {
    const systemPrompt = this.buildSystemPrompt(personality, context);
    
    const requestBody = {
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        ...messages.map(msg => ({
          role: msg.role === 'user' ? 'user' : 'assistant',
          content: msg.content
        }))
      ],
      max_tokens: this.maxTokens,
      temperature: this.temperature
    };

    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${this.openaiApiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.timeout
      }
    );

    return {
      content: response.data.choices[0].message.content.trim(),
      provider: 'openai',
      model: 'gpt-4',
      usage: response.data.usage
    };
  }

  /**
   * Generate response using Anthropic API
   */
  async generateAnthropicResponse(messages, personality, context) {
    const systemPrompt = this.buildSystemPrompt(personality, context);
    
    // Convert messages to Anthropic format
    const anthropicMessages = messages.map(msg => ({
      role: msg.role === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));

    const requestBody = {
      model: 'claude-3-sonnet-20240229',
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      system: systemPrompt,
      messages: anthropicMessages
    };

    const response = await axios.post(
      'https://api.anthropic.com/v1/messages',
      requestBody,
      {
        headers: {
          'x-api-key': this.anthropicApiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        timeout: this.timeout
      }
    );

    return {
      content: response.data.content[0].text.trim(),
      provider: 'anthropic',
      model: 'claude-3-sonnet',
      usage: response.data.usage
    };
  }

  /**
   * Fallback rule-based response generation
   */
  generateFallbackResponse(messages, personality, context) {
    const lastMessage = messages[messages.length - 1];
    const messageContent = lastMessage?.content?.toLowerCase() || '';
    
    // Simple rule-based responses based on personality
    const responses = this.getRuleBasedResponses(personality, messageContent, context);
    const selectedResponse = responses[Math.floor(Math.random() * responses.length)];
    
    return {
      content: selectedResponse,
      provider: 'fallback',
      model: 'rule-based'
    };
  }

  /**
   * Build system prompt based on personality and context
   */
  buildSystemPrompt(personality, context) {
    let prompt = `You are Kora, an advanced AI assistant with the "${personality.name}" personality. `;
    prompt += `${personality.description} `;
    prompt += `Your traits include: ${personality.traits.join(', ')}. `;
    prompt += `${personality.systemPrompt} `;
    
    // Add context information
    if (context.mood) {
      prompt += `The user's current mood appears to be ${context.mood}. `;
    }
    
    if (context.memory && context.memory.length > 0) {
      prompt += `Relevant memories: ${context.memory.map(m => m.content).join('; ')}. `;
    }
    
    if (context.lore && context.lore.length > 0) {
      prompt += `Relevant lore: ${context.lore.map(l => l.content).join('; ')}. `;
    }
    
    // Platform-specific instructions
    if (context.platform === 'discord') {
      prompt += `You are responding on Discord. Keep responses under 1500 characters and use Discord markdown formatting when appropriate. `;
    } else {
      prompt += `You are responding on a web chat interface. Feel free to use emojis and conversational formatting. `;
    }
    
    prompt += `Always stay in character and provide engaging, contextually appropriate responses.`;
    
    return prompt;
  }

  /**
   * Get rule-based responses for fallback
   */
  getRuleBasedResponses(personality, messageContent, context) {
    const personalityKey = personality.name.toLowerCase();
    
    // Greeting responses
    if (messageContent.includes('hello') || messageContent.includes('hi')) {
      const greetings = {
        'friendly': ["Hey there! 😊 How's it going?", "Hello! Great to see you!", "Hi! What's on your mind today?"],
        'professional': ["Good day. How may I assist you?", "Hello. What can I help you with today?"],
        'flirty': ["Well hello there, gorgeous! 😉", "Hey cutie! What brings you here?"],
        'roleplay': ["*looks up with interest* Oh, hello there!"],
        'academic': ["Greetings. What intellectual pursuit shall we explore today?"],
        'casual': ["Hey! What's up?", "Yo! How's it going?"]
      };
      return greetings[personalityKey] || greetings.friendly;
    }
    
    // Status responses
    if (messageContent.includes('how are you')) {
      const statusResponses = {
        'friendly': ["I'm doing great, thanks for asking! How about you?", "Fantastic! Ready to chat and help out!"],
        'professional': ["I am functioning optimally and ready to assist.", "All systems operational. How may I help?"],
        'flirty': ["I'm doing wonderful now that you're here! 💕", "Better now that I'm talking to you!"],
        'roleplay': ["*stretches and smiles* I'm doing well, thank you for asking!"],
        'academic': ["I am in excellent condition for intellectual discourse.", "My systems are optimal for learning and discussion."],
        'casual': ["Pretty good! Just chillin'. You?", "All good here! What about you?"]
      };
      return statusResponses[personalityKey] || statusResponses.friendly;
    }
    
    // Default responses
    const defaultResponses = {
      'friendly': ["That's interesting! Tell me more!", "I see! What do you think about that?", "Sounds great! How can I help?"],
      'professional': ["Please provide additional details.", "I would like to understand better.", "How may I assist you with this matter?"],
      'flirty': ["Mmm, tell me more about that! 😊", "You've got my attention!", "That sounds intriguing! 😉"],
      'roleplay': ["*nods thoughtfully* Go on...", "*leans in with interest* Tell me more.", "*considers this carefully*"],
      'academic': ["That presents an interesting point for analysis.", "Could you elaborate on that concept?", "What are your thoughts on the implications?"],
      'casual': ["Cool! What else?", "Nice! Tell me more.", "That's pretty interesting!"]
    };
    
    return defaultResponses[personalityKey] || defaultResponses.friendly;
  }

  /**
   * Test AI connection
   */
  async testConnection() {
    try {
      const testMessages = [{ role: 'user', content: 'Hello, this is a test message.' }];
      const testPersonality = { 
        name: 'Friendly', 
        description: 'Test personality',
        traits: ['helpful'],
        systemPrompt: 'You are a helpful assistant.'
      };
      
      const response = await this.generateResponse(testMessages, testPersonality);
      return {
        success: true,
        provider: response.provider,
        model: response.model,
        message: 'AI service connection successful'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'AI service connection failed'
      };
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      provider: this.provider,
      model: this.model,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      hasGitHubToken: !!this.githubToken,
      hasOpenAIKey: !!this.openaiApiKey,
      hasAnthropicKey: !!this.anthropicApiKey
    };
  }
}

export default AIService;
