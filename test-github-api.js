import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

async function testGitHubAPI() {
  const githubToken = process.env.GITHUB_TOKEN;
  const model = process.env.AI_MODEL || 'gpt-4o';
  const apiUrl = 'https://models.inference.ai.azure.com';

  console.log('🔍 Testing GitHub Models API...');
  console.log('Token:', githubToken ? githubToken.substring(0, 20) + '...' : 'NOT FOUND');
  console.log('Model:', model);
  console.log('URL:', apiUrl);

  const requestBody = {
    messages: [
      { 
        role: 'system', 
        content: 'Você <PERSON>, uma assistente de IA amigável que fala português brasileiro. Responda sempre em português.' 
      },
      { 
        role: 'user', 
        content: 'Olá, como você está?' 
      }
    ],
    model: model,
    max_tokens: 150,
    temperature: 0.7,
    stream: false
  };

  try {
    console.log('📤 Sending request...');
    const response = await axios.post(
      `${apiUrl}/chat/completions`,
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${githubToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    console.log('✅ Success!');
    console.log('Status:', response.status);
    console.log('Response:', response.data.choices[0].message.content);

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testGitHubAPI();
