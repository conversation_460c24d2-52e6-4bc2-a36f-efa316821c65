import { Client, GatewayIntentBits, Events } from 'discord.js';
import dotenv from 'dotenv';
import fs from 'fs-extra';

dotenv.config();

async function testToken() {
  const logFile = './token-test-new.log';
  
  const log = async (message) => {
    console.log(message);
    await fs.appendFile(logFile, `${new Date().toISOString()} - ${message}\n`).catch(() => {});
  };
  
  await log('🧪 Testando token do Discord...');
  
  const token = process.env.DISCORD_TOKEN;
  await log(`🔑 Token: ${token ? `${token.substring(0, 30)}...` : 'NÃO ENCONTRADO'}`);
  
  if (!token) {
    await log('❌ DISCORD_TOKEN não encontrado');
    return;
  }
  
  try {
    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.DirectMessageReactions,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessageReactions
      ]
    });
    
    client.once(Events.ClientReady, async (readyClient) => {
      await log(`✅ Bot conectado: ${readyClient.user.tag}`);
      await log(`🏠 Servidores: ${readyClient.guilds.cache.size}`);
      await log(`👥 Usuários: ${readyClient.users.cache.size}`);
      
      for (const guild of readyClient.guilds.cache.values()) {
        await log(`📍 Servidor: ${guild.name} (${guild.memberCount} membros)`);
      }
      
      await log('🎯 Bot pronto para receber mensagens!');
    });
    
    client.on(Events.MessageCreate, async (message) => {
      await log(`📨 MENSAGEM: "${message.content}" de ${message.author.username}`);
      await log(`📍 Canal: tipo=${message.channel.type}, id=${message.channel.id}`);
      
      if (!message.author.bot && message.content.toLowerCase().includes('test')) {
        try {
          await message.reply('✅ Teste bem-sucedido! O bot está funcionando!');
          await log('✅ Resposta de teste enviada');
        } catch (error) {
          await log(`❌ Erro ao responder: ${error.message}`);
        }
      }
    });
    
    client.on(Events.Error, async (error) => {
      await log(`❌ Erro do cliente: ${error.message}`);
    });
    
    await log('🔄 Tentando conectar...');
    await client.login(token);
    
    // Manter vivo por 3 minutos
    setTimeout(async () => {
      await log('⏰ Teste finalizado');
      client.destroy();
      process.exit(0);
    }, 180000);
    
  } catch (error) {
    await log(`❌ Erro de conexão: ${error.message}`);
    process.exit(1);
  }
}

testToken();
