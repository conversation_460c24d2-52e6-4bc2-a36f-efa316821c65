# Kora 2.0 - Advanced AI Chatbot

Kora 2.0 is a highly advanced AI assistant designed to offer emotionally intelligent, context-aware, and personality-driven conversations. Built with modern Node.js and featuring both web and Discord integrations.

## 🌟 Features

### Core Capabilities
- **Personality System**: Multiple personality modes (Friendly, Professional, Flirty, Roleplay, Academic, Casual)
- **Context Awareness**: Maintains conversation context and references previous discussions
- **Memory Management**: Persistent memory system for learning user preferences
- **Mood Analysis**: Adapts responses based on user sentiment and mood
- **Multi-Platform**: Supports both web interface and Discord bot

### Advanced Features
- **Command System**: Rich command interface for customization
- **Lore Integration**: Import and use custom world-building data
- **Data Export/Import**: Backup and restore conversation data
- **Context Compression**: Intelligent context management for long conversations
- **Entity Extraction**: Identifies and remembers important information

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- GitHub Personal Access Token (for AI responses)
- Discord Bot Token (optional, for Discord integration)

### Installation

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd kora-2.0
   npm install
   ```

2. **GitHub Models API Setup**
   ```bash
   # Get your GitHub Personal Access Token
   # 1. Go to GitHub Settings > Developer settings > Personal access tokens
   # 2. Generate new token with 'repo' and 'read:org' scopes
   # 3. Copy the token
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your GitHub token and other settings
   ```

4. **Start the application**
   ```bash
   # Development mode (web interface only)
   npm run dev

   # Production mode
   npm start

   # Discord bot only
   npm run discord
   ```

5. **Access the interface**
   - Web Interface: http://localhost:3000
   - API Endpoint: http://localhost:3000/api
   - Health Check: http://localhost:3000/health
   - AI Status: http://localhost:3000/api/ai/status

## 🎭 Personality System

Kora features six distinct personality modes:

- **Friendly**: Warm, engaging, and slightly playful
- **Professional**: Formal, structured, business-oriented
- **Flirty**: Playful, charming, subtly romantic
- **Roleplay**: Immersive character for roleplay scenarios
- **Academic**: Intellectual, analytical, educational
- **Casual**: Relaxed, informal, like talking to a friend

Switch personalities using: `/set_personality <name>`

## 🤖 AI Integration

Kora 2.0 uses **GitHub Models API** with **ChatGPT 4.1** for intelligent responses:

### Primary AI Provider: GitHub Models
- **Model**: GPT-4o (ChatGPT 4.1)
- **Provider**: GitHub Models API
- **Fallback**: Rule-based responses if API unavailable

### Setup GitHub Models API
1. **Get GitHub Personal Access Token**:
   - Go to GitHub Settings → Developer settings → Personal access tokens
   - Generate new token (classic) with `repo` and `read:org` scopes
   - Copy the token

2. **Configure Environment**:
   ```env
   GITHUB_TOKEN=your_github_personal_access_token
   AI_MODEL=gpt-4o
   AI_PROVIDER=github
   MAX_TOKENS=1000
   TEMPERATURE=0.7
   ```

3. **Test AI Connection**:
   ```bash
   curl http://localhost:3000/api/ai/status
   ```

### Alternative AI Providers
- **OpenAI**: Set `AI_PROVIDER=openai` and `OPENAI_API_KEY`
- **Anthropic**: Set `AI_PROVIDER=anthropic` and `ANTHROPIC_API_KEY`
- **Fallback**: Rule-based responses (no API required)

## 💬 Command System

### Basic Commands
- `/help` - Show available commands
- `/status` - Display current session information
- `/set_personality <name>` - Change personality mode
- `/reset_context` - Clear conversation history

### Memory & Data Commands
- `/save_memory <text>` - Save important information
- `/load_memory <file>` - Import memory from JSON file
- `/load_lore <file>` - Import lore/world data
- `/export_data [memory|lore]` - Export data to file
- `/stats` - Show detailed statistics

## 🔧 Configuration

### Environment Variables
```env
# GitHub Models API (Primary AI Provider)
GITHUB_TOKEN=your_github_personal_access_token
AI_MODEL=gpt-4o
AI_PROVIDER=github
MAX_TOKENS=1000
TEMPERATURE=0.7

# Discord Bot (optional)
DISCORD_TOKEN=your_discord_bot_token
DISCORD_CLIENT_ID=your_client_id

# Server Configuration
PORT=3000
NODE_ENV=development

# Features
ENABLE_DISCORD=true
ENABLE_WEB=true
LOG_LEVEL=info
```

### Configuration Files
- `config/default.json` - Base configuration
- `config/development.json` - Development overrides
- `config/production.json` - Production settings
- `config/local.json` - Local overrides (optional)

## 📁 Project Structure

```
kora-2.0/
├── src/
│   ├── core/           # Core chatbot engine
│   ├── discord/        # Discord bot integration
│   ├── web-api/        # REST API routes
│   └── utils/          # Utility modules
├── web/                # Web interface
├── config/             # Configuration files
├── data/               # Data storage
│   ├── logs/          # Application logs
│   ├── exports/       # Data exports
│   └── sample_*.json  # Sample data files
└── docs/               # Documentation
```

## 🌐 API Endpoints

### Chat API
- `POST /api/chat` - Send message to chatbot
- `GET /api/personalities` - Get available personalities
- `POST /api/personality` - Set session personality

### Session Management
- `GET /api/session/:id` - Get session info
- `DELETE /api/session/:id` - Delete session

### Data Management
- `POST /api/memory` - Add memory entry
- `POST /api/lore` - Load lore data
- `GET /api/stats` - Get system statistics

### AI Service
- `GET /api/ai/status` - Get AI service status and connection test

## 🤖 Discord Integration

### Setup Discord Bot
1. Create application at https://discord.com/developers/applications
2. Create bot and copy token to `.env`
3. Invite bot to server with appropriate permissions
4. Start with `npm run discord`

### Discord Features
- Responds to mentions and DMs
- Automatic message splitting for long responses
- Markdown formatting support
- Per-channel session management

## 💾 Data Management

### Memory System
- Automatic importance detection
- Searchable memory entries
- Category-based organization
- Export/import capabilities

### Lore Integration
- Custom world-building data
- Context-aware lore retrieval
- Category-based organization
- Access tracking

## 🔍 Development

### Running Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev  # Starts with nodemon for auto-reload
```

### Building Web Interface
```bash
npm run build  # Builds optimized web interface
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:3000/health
```

### Statistics
```bash
curl http://localhost:3000/api/stats
```

### Logs
- Development: Console output
- Production: `data/logs/kora.log`

## 🛠️ Customization

### Adding New Personalities
Edit `config/default.json` or environment-specific config:

```json
{
  "personalities": {
    "custom": {
      "name": "Custom Personality",
      "description": "Your custom personality description",
      "traits": ["trait1", "trait2"],
      "systemPrompt": "Your system prompt",
      "responseStyle": "casual",
      "emojiUsage": "moderate"
    }
  }
}
```

### Custom Commands
Extend `src/utils/CommandProcessor.js` to add new commands.

### Memory Categories
Customize memory categories in `src/utils/MemoryManager.js`.

## 🚨 Troubleshooting

### Common Issues
1. **Port already in use**: Change PORT in `.env`
2. **Discord bot not responding**: Check token and permissions
3. **Memory not persisting**: Ensure `data/` directory is writable
4. **Web interface not loading**: Run `npm run build` first

### Debug Mode
Set `LOG_LEVEL=debug` in `.env` for detailed logging.

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review the logs in `data/logs/`
- Open an issue on the repository

---

**Kora 2.0** - Your emotionally intelligent AI companion 🤖💕
