import fs from 'fs-extra';
import path from 'path';
import dotenv from 'dotenv';

/**
 * Configuration Manager - Handles loading and merging configuration files
 */
class ConfigManager {
  constructor() {
    this.config = {};
    this.environment = process.env.NODE_ENV || 'development';
    this.configDir = path.join(process.cwd(), 'config');

    // Initialize synchronously
    this.initializeSync();
  }

  /**
   * Initialize configuration synchronously
   */
  initializeSync() {
    try {
      // Load environment variables
      dotenv.config();

      // Load default configuration
      this.loadConfigFileSync('default.json');

      // Load environment-specific configuration
      const envConfigFile = `${this.environment}.json`;
      if (fs.pathExistsSync(path.join(this.configDir, envConfigFile))) {
        this.loadConfigFileSync(envConfigFile);
      }

      // Load local overrides if they exist
      const localConfigFile = 'local.json';
      if (fs.pathExistsSync(path.join(this.configDir, localConfigFile))) {
        this.loadConfigFileSync(localConfigFile);
      }

      // Apply environment variable overrides
      this.applyEnvironmentOverrides();

      console.log(`Configuration loaded for environment: ${this.environment}`);
    } catch (error) {
      console.error('Failed to load configuration:', error);
      throw error;
    }
  }

  /**
   * Load configuration from files and environment (async version)
   */
  async loadConfiguration() {
    try {
      // Load environment variables
      dotenv.config();

      // Load default configuration
      await this.loadConfigFile('default.json');

      // Load environment-specific configuration
      const envConfigFile = `${this.environment}.json`;
      if (await fs.pathExists(path.join(this.configDir, envConfigFile))) {
        await this.loadConfigFile(envConfigFile);
      }

      // Load local overrides if they exist
      const localConfigFile = 'local.json';
      if (await fs.pathExists(path.join(this.configDir, localConfigFile))) {
        await this.loadConfigFile(localConfigFile);
      }

      // Apply environment variable overrides
      this.applyEnvironmentOverrides();

      console.log(`Configuration loaded for environment: ${this.environment}`);
    } catch (error) {
      console.error('Failed to load configuration:', error);
      throw error;
    }
  }

  /**
   * Load a specific configuration file synchronously
   */
  loadConfigFileSync(filename) {
    try {
      const filePath = path.join(this.configDir, filename);
      const fileConfig = fs.readJsonSync(filePath);
      this.config = this.deepMerge(this.config, fileConfig);
    } catch (error) {
      if (filename !== 'local.json') { // local.json is optional
        console.warn(`Could not load config file ${filename}:`, error.message);
      }
    }
  }

  /**
   * Load a specific configuration file (async version)
   */
  async loadConfigFile(filename) {
    try {
      const filePath = path.join(this.configDir, filename);
      const fileConfig = await fs.readJson(filePath);
      this.config = this.deepMerge(this.config, fileConfig);
    } catch (error) {
      if (filename !== 'local.json') { // local.json is optional
        console.warn(`Could not load config file ${filename}:`, error.message);
      }
    }
  }

  /**
   * Apply environment variable overrides
   */
  applyEnvironmentOverrides() {
    // Server configuration
    if (process.env.PORT) {
      this.config.server = this.config.server || {};
      this.config.server.port = parseInt(process.env.PORT);
    }
    
    if (process.env.HOST) {
      this.config.server = this.config.server || {};
      this.config.server.host = process.env.HOST;
    }

    // Discord configuration
    if (process.env.DISCORD_TOKEN) {
      this.config.discord = this.config.discord || {};
      this.config.discord.token = process.env.DISCORD_TOKEN;
    }

    if (process.env.DISCORD_CLIENT_ID) {
      this.config.discord = this.config.discord || {};
      this.config.discord.clientId = process.env.DISCORD_CLIENT_ID;
    }

    // Database configuration
    if (process.env.DATABASE_URL) {
      this.config.database = this.config.database || {};
      this.config.database.url = process.env.DATABASE_URL;
    }

    // Logging configuration
    if (process.env.LOG_LEVEL) {
      this.config.logging = this.config.logging || {};
      this.config.logging.level = process.env.LOG_LEVEL;
    }

    // Feature flags
    if (process.env.ENABLE_DISCORD !== undefined) {
      this.config.discord = this.config.discord || {};
      this.config.discord.enabled = process.env.ENABLE_DISCORD === 'true';
    }

    if (process.env.ENABLE_WEB !== undefined) {
      this.config.web = this.config.web || {};
      this.config.web.enabled = process.env.ENABLE_WEB === 'true';
    }
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Get configuration value by path
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let current = this.config;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }

  /**
   * Set configuration value by path
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * Get all configuration
   */
  getAll() {
    return { ...this.config };
  }

  /**
   * Get server configuration
   */
  getServerConfig() {
    return this.get('server', {
      port: 3000,
      host: 'localhost'
    });
  }

  /**
   * Get Discord configuration
   */
  getDiscordConfig() {
    return this.get('discord', {
      enabled: false,
      commandPrefix: '/',
      maxMessageLength: 1500
    });
  }

  /**
   * Get web configuration
   */
  getWebConfig() {
    return this.get('web', {
      enabled: true,
      cors: {
        origin: ['http://localhost:3000'],
        credentials: true
      }
    });
  }

  /**
   * Get chatbot configuration
   */
  getChatbotConfig() {
    return this.get('chatbot', {
      defaultPersonality: 'friendly',
      contextWindow: 10,
      maxMemoryEntries: 100,
      responseTimeout: 30000
    });
  }

  /**
   * Get personalities configuration
   */
  getPersonalities() {
    return this.get('personalities', {});
  }

  /**
   * Get commands configuration
   */
  getCommandsConfig() {
    return this.get('commands', {
      enabled: true,
      prefix: '/',
      availableCommands: ['help', 'status']
    });
  }

  /**
   * Get logging configuration
   */
  getLoggingConfig() {
    return this.get('logging', {
      level: 'info',
      file: './data/logs/kora.log',
      console: true
    });
  }

  /**
   * Get features configuration
   */
  getFeaturesConfig() {
    return this.get('features', {
      contextCompression: true,
      moodAnalysis: true,
      entityExtraction: false,
      memorySearch: true,
      loreIntegration: true
    });
  }

  /**
   * Check if a feature is enabled
   */
  isFeatureEnabled(featureName) {
    return this.get(`features.${featureName}`, false);
  }

  /**
   * Validate configuration
   */
  validate() {
    const errors = [];

    // Validate server configuration
    const serverConfig = this.getServerConfig();
    if (!serverConfig.port || serverConfig.port < 1 || serverConfig.port > 65535) {
      errors.push('Invalid server port configuration');
    }

    // Validate Discord configuration if enabled
    const discordConfig = this.getDiscordConfig();
    if (discordConfig.enabled) {
      if (!process.env.DISCORD_TOKEN) {
        errors.push('Discord is enabled but DISCORD_TOKEN environment variable is not set');
      }
    }

    // Validate personalities
    const personalities = this.getPersonalities();
    if (Object.keys(personalities).length === 0) {
      errors.push('No personalities configured');
    }

    const chatbotConfig = this.getChatbotConfig();
    if (!personalities[chatbotConfig.defaultPersonality]) {
      errors.push(`Default personality '${chatbotConfig.defaultPersonality}' is not defined`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get configuration summary for logging
   */
  getSummary() {
    const serverConfig = this.getServerConfig();
    const discordConfig = this.getDiscordConfig();
    const webConfig = this.getWebConfig();
    const chatbotConfig = this.getChatbotConfig();
    const features = this.getFeaturesConfig();

    return {
      environment: this.environment,
      server: {
        enabled: true,
        host: serverConfig.host,
        port: serverConfig.port
      },
      discord: {
        enabled: discordConfig.enabled
      },
      web: {
        enabled: webConfig.enabled
      },
      chatbot: {
        defaultPersonality: chatbotConfig.defaultPersonality,
        contextWindow: chatbotConfig.contextWindow,
        personalities: Object.keys(this.getPersonalities()).length
      },
      features: Object.entries(features)
        .filter(([_, enabled]) => enabled)
        .map(([name, _]) => name)
    };
  }

  /**
   * Reload configuration
   */
  async reload() {
    this.config = {};
    await this.loadConfiguration();
  }
}

// Export singleton instance
const configManager = new ConfigManager();
export default configManager;
