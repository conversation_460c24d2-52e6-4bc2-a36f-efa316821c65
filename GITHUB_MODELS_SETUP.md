# GitHub Models API Setup Guide

## 🤖 AI Integration with ChatGPT 4.1

Kora 2.0 now supports **GitHub Models API** integration with **ChatGPT 4.1 (GPT-4o)** for intelligent, context-aware responses.

## 🚀 Quick Setup

### 1. Get GitHub Personal Access Token

1. **Go to GitHub Settings**:
   - Visit: https://github.com/settings/tokens
   - Or: GitHub Profile → Settings → Developer settings → Personal access tokens

2. **Generate New Token (Classic)**:
   - Click "Generate new token" → "Generate new token (classic)"
   - Give it a descriptive name: `Kora 2.0 AI Chatbot`
   - Set expiration as needed (recommend 90 days or no expiration for development)

3. **Select Required Scopes**:
   ```
   ✅ repo (Full control of private repositories)
   ✅ read:org (Read org and team membership, read org projects)
   ```

4. **Generate and Copy Token**:
   - Click "Generate token"
   - **IMPORTANT**: Copy the token immediately (you won't see it again!)

### 2. Configure Environment

1. **Update your `.env` file**:
   ```env
   # GitHub Models API Configuration
   GITHUB_TOKEN=ghp_your_actual_token_here
   AI_MODEL=gpt-4o
   AI_PROVIDER=github
   MAX_TOKENS=1000
   TEMPERATURE=0.7
   AI_TIMEOUT=30000
   ```

2. **Alternative Models Available**:
   ```env
   # For different ChatGPT models
   AI_MODEL=gpt-4o              # ChatGPT 4.1 (recommended)
   AI_MODEL=gpt-4               # ChatGPT 4
   AI_MODEL=gpt-3.5-turbo       # ChatGPT 3.5
   ```

### 3. Test the Integration

1. **Start the server**:
   ```bash
   npm start
   ```

2. **Check AI status**:
   ```bash
   curl http://localhost:3000/api/ai/status
   ```

3. **Expected successful response**:
   ```json
   {
     "success": true,
     "aiService": {
       "provider": "github",
       "model": "gpt-4o",
       "hasGitHubToken": true,
       "connectionTest": {
         "success": true,
         "provider": "github",
         "model": "gpt-4o",
         "message": "AI service connection successful"
       }
     }
   }
   ```

4. **Test chat with AI**:
   ```bash
   curl -X POST http://localhost:3000/api/chat \
     -H "Content-Type: application/json" \
     -d '{"message": "Tell me a creative story about a robot learning to paint"}'
   ```

## ⚙️ Configuration Options

### AI Parameters

```env
# Model Selection
AI_MODEL=gpt-4o                    # Model to use
AI_PROVIDER=github                 # Primary provider

# Response Control
MAX_TOKENS=1000                    # Maximum response length
TEMPERATURE=0.7                    # Creativity (0.0-1.0)
AI_TIMEOUT=30000                   # Request timeout (ms)

# GitHub API
GITHUB_API_URL=https://models.inference.ai.azure.com
```

### Personality Integration

The AI service automatically integrates with Kora's personality system:

- **System Prompts**: Each personality has custom prompts for the AI
- **Context Awareness**: AI receives conversation history and memory
- **Mood Adaptation**: AI adjusts tone based on user sentiment
- **Platform Formatting**: Responses formatted for Discord/Web

## 🔄 Fallback System

Kora includes a robust fallback system:

1. **Primary**: GitHub Models API (ChatGPT 4.1)
2. **Secondary**: OpenAI API (if configured)
3. **Tertiary**: Anthropic API (if configured)
4. **Fallback**: Rule-based responses (always available)

### Configure Multiple Providers

```env
# Primary: GitHub Models
GITHUB_TOKEN=your_github_token
AI_PROVIDER=github

# Backup: OpenAI
OPENAI_API_KEY=your_openai_key

# Backup: Anthropic
ANTHROPIC_API_KEY=your_anthropic_key
```

## 🎭 Personality Examples with AI

### Friendly Personality
```
User: "I'm feeling a bit down today"
AI Response: "I'm sorry to hear you're feeling down! 😊 Sometimes we all have those days. Would you like to talk about what's bothering you, or would you prefer a distraction? I'm here either way and happy to help however I can!"
```

### Professional Personality
```
User: "I need help with project management"
AI Response: "I'd be pleased to assist you with project management. To provide the most relevant guidance, could you specify the type of project, team size, and particular challenges you're facing? I can offer structured approaches and best practices tailored to your situation."
```

### Flirty Personality
```
User: "What should I do tonight?"
AI Response: "Well hello there! 😉 Tonight could be whatever you want it to be! Are you in the mood for something adventurous, cozy, or maybe a little bit of both? I'd love to help you plan something fun! 💕"
```

## 🔍 Monitoring & Debugging

### Check AI Service Status
```bash
# Get detailed status
curl http://localhost:3000/api/ai/status

# Get general stats (includes AI info)
curl http://localhost:3000/api/stats
```

### Debug Mode
```env
LOG_LEVEL=debug
```

### Common Issues

1. **"AI service connection failed"**
   - Check GitHub token is valid
   - Verify token has correct scopes
   - Check internet connection

2. **"Using fallback responses"**
   - Normal behavior when no AI provider configured
   - Check environment variables
   - Verify API quotas/limits

3. **Slow responses**
   - Adjust `AI_TIMEOUT` setting
   - Check `MAX_TOKENS` (lower = faster)
   - Monitor API rate limits

## 💡 Tips for Best Results

### Optimize Token Usage
```env
MAX_TOKENS=800        # For faster responses
TEMPERATURE=0.6       # For more consistent responses
TEMPERATURE=0.8       # For more creative responses
```

### Context Management
- Kora automatically sends last 5 messages to AI
- Memory and lore are included in context
- Personality traits guide response style

### Rate Limiting
- GitHub Models API has generous limits
- Fallback system prevents service interruption
- Monitor usage in logs

## 🔐 Security Notes

- **Never commit `.env` files** to version control
- **Rotate tokens regularly** for production use
- **Use environment-specific tokens** for different deployments
- **Monitor token usage** in GitHub settings

## 🚀 Production Deployment

```env
# Production settings
NODE_ENV=production
AI_PROVIDER=github
MAX_TOKENS=800
TEMPERATURE=0.6
AI_TIMEOUT=25000
```

## 📞 Support

If you encounter issues:

1. Check the AI status endpoint: `/api/ai/status`
2. Review server logs for error messages
3. Verify GitHub token permissions
4. Test with fallback mode first

The chatbot will continue working with rule-based responses even if AI integration fails, ensuring uninterrupted service.

---

**Ready to chat with AI-powered Kora!** 🤖✨
