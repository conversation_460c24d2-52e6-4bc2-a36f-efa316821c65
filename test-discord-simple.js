import { Client, GatewayIntentBits, Events, ChannelType } from 'discord.js';
import dotenv from 'dotenv';

dotenv.config();

async function testDiscordSimple() {
  console.log('🧪 Teste simples do Discord Bot...');
  
  const token = process.env.DISCORD_TOKEN;
  console.log('🔑 Token Discord:', token ? `${token.substring(0, 20)}...` : 'NÃO ENCONTRADO');
  
  if (!token) {
    console.error('❌ DISCORD_TOKEN não encontrado no .env');
    return;
  }
  
  try {
    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.DirectMessageReactions
      ]
    });
    
    client.once(Events.ClientReady, (readyClient) => {
      console.log('✅ Discord Bot conectado como:', readyClient.user.tag);
      console.log('🏠 Servidores:', readyClient.guilds.cache.size);
      console.log('👥 Usuários visíveis:', readyClient.users.cache.size);
      
      // Listar servidores
      readyClient.guilds.cache.forEach(guild => {
        console.log(`📍 Servidor: ${guild.name} (${guild.memberCount} membros)`);
      });
    });
    
    client.on(Events.MessageCreate, async (message) => {
      console.log(`📨 MENSAGEM RECEBIDA: "${message.content}" de ${message.author.username}`);
      console.log(`📍 Canal: tipo=${message.channel.type}, id=${message.channel.id}`);
      console.log(`🤖 É bot: ${message.author.bot}`);
      console.log(`📝 Tipo DM: ${ChannelType.DM}`);
      
      if (message.author.bot) {
        console.log('🤖 Ignorando mensagem de bot');
        return;
      }
      
      const isDM = message.channel.type === ChannelType.DM;
      console.log(`💬 É DM: ${isDM}`);
      
      if (isDM) {
        console.log('✅ RESPONDENDO EM DM...');
        try {
          await message.reply('Oi! Recebi sua mensagem! 🎉');
          console.log('✅ Resposta enviada com sucesso!');
        } catch (error) {
          console.error('❌ Erro ao enviar resposta:', error);
        }
      }
    });
    
    client.on(Events.Error, (error) => {
      console.error('❌ Erro do Discord:', error);
    });
    
    console.log('🔄 Conectando...');
    await client.login(token);
    
    // Manter vivo por 2 minutos para teste
    setTimeout(() => {
      console.log('⏰ Teste finalizado, desconectando...');
      client.destroy();
      process.exit(0);
    }, 120000);
    
  } catch (error) {
    console.error('❌ Erro ao conectar:', error.message);
    process.exit(1);
  }
}

testDiscordSimple();
