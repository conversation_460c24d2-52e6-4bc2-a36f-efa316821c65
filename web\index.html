<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kora 2.0 - AI Chatbot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .personality-selector {
            position: absolute;
            top: 15px;
            right: 20px;
        }

        .personality-selector select {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
        }

        .personality-selector select option {
            background: #667eea;
            color: white;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-in;
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.bot {
            align-self: flex-start;
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message.command {
            align-self: flex-start;
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-style: italic;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input-container {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            align-self: flex-start;
            padding: 12px 16px;
            background: #f1f3f4;
            border-radius: 18px;
            color: #666;
            font-style: italic;
            animation: pulse 1.5s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .status-bar {
            padding: 8px 20px;
            background: #e8f4fd;
            border-top: 1px solid #bee5eb;
            font-size: 12px;
            color: #0c5460;
            text-align: center;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .personality-selector {
                position: static;
                margin-top: 10px;
            }
            
            .chat-header {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 Kora 2.0</h1>
            <p>Your AI companion with personality</p>
            <div class="personality-selector">
                <select id="personalitySelect">
                    <option value="friendly">Friendly</option>
                    <option value="professional">Professional</option>
                    <option value="flirty">Flirty</option>
                    <option value="roleplay">Roleplay</option>
                </select>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div>Hello! I'm Kora, your AI companion. I can adapt my personality to match your preferences and remember our conversation context. How can I help you today? 😊</div>
                <div class="message-time" id="welcomeTime"></div>
            </div>
        </div>
        
        <div class="status-bar" id="statusBar">
            Ready to chat • Session: <span id="sessionId">Initializing...</span>
        </div>
        
        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <textarea 
                    id="chatInput" 
                    class="chat-input" 
                    placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
                    rows="1"
                ></textarea>
                <button type="submit" class="send-button" id="sendButton">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </form>
        </div>
    </div>

    <script>
        class KoraChatInterface {
            constructor() {
                this.sessionId = this.generateSessionId();
                this.currentPersonality = 'friendly';
                this.isTyping = false;
                
                this.initializeElements();
                this.setupEventListeners();
                this.loadPersonalities();
                this.updateWelcomeTime();
                this.updateSessionDisplay();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.chatForm = document.getElementById('chatForm');
                this.sendButton = document.getElementById('sendButton');
                this.personalitySelect = document.getElementById('personalitySelect');
                this.statusBar = document.getElementById('statusBar');
                this.sessionIdSpan = document.getElementById('sessionId');
            }

            setupEventListeners() {
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
                this.chatInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
                this.chatInput.addEventListener('input', () => this.autoResize());
                this.personalitySelect.addEventListener('change', (e) => this.changePersonality(e.target.value));
            }

            generateSessionId() {
                return 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            updateWelcomeTime() {
                const welcomeTimeElement = document.getElementById('welcomeTime');
                if (welcomeTimeElement) {
                    welcomeTimeElement.textContent = new Date().toLocaleTimeString();
                }
            }

            updateSessionDisplay() {
                this.sessionIdSpan.textContent = this.sessionId.substr(-8);
            }

            async loadPersonalities() {
                try {
                    const response = await fetch('/api/personalities');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.personalitySelect.innerHTML = '';
                        data.personalities.forEach(personality => {
                            const option = document.createElement('option');
                            option.value = personality.id;
                            option.textContent = personality.name;
                            option.title = personality.description;
                            this.personalitySelect.appendChild(option);
                        });
                        
                        this.personalitySelect.value = data.default;
                        this.currentPersonality = data.default;
                    }
                } catch (error) {
                    console.error('Failed to load personalities:', error);
                }
            }

            async changePersonality(personality) {
                try {
                    const response = await fetch('/api/personality', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            sessionId: this.sessionId,
                            personality: personality
                        })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        this.currentPersonality = personality;
                        this.addMessage('system', `Personality changed to "${data.personality.name}". ${data.personality.description}`, true);
                    }
                } catch (error) {
                    console.error('Failed to change personality:', error);
                }
            }

            handleKeyDown(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.chatForm.dispatchEvent(new Event('submit'));
                }
            }

            autoResize() {
                this.chatInput.style.height = 'auto';
                this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
            }

            async handleSubmit(e) {
                e.preventDefault();
                
                const message = this.chatInput.value.trim();
                if (!message || this.isTyping) return;
                
                this.addMessage('user', message);
                this.chatInput.value = '';
                this.autoResize();
                this.setTyping(true);
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: message,
                            sessionId: this.sessionId,
                            userSettings: {
                                personality: this.currentPersonality
                            }
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        this.addMessage('bot', data.response, data.isCommand);
                    } else {
                        this.addMessage('bot', 'Sorry, I encountered an error. Please try again.');
                    }
                } catch (error) {
                    console.error('Chat error:', error);
                    this.addMessage('bot', 'Sorry, I couldn\'t connect to the server. Please check your connection and try again.');
                } finally {
                    this.setTyping(false);
                }
            }

            addMessage(sender, content, isCommand = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}${isCommand ? ' command' : ''}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.textContent = content;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString();
                
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timeDiv);
                
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            setTyping(typing) {
                this.isTyping = typing;
                this.sendButton.disabled = typing;
                
                if (typing) {
                    const typingDiv = document.createElement('div');
                    typingDiv.className = 'typing-indicator';
                    typingDiv.id = 'typingIndicator';
                    typingDiv.textContent = 'Kora is typing...';
                    this.chatMessages.appendChild(typingDiv);
                } else {
                    const typingIndicator = document.getElementById('typingIndicator');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }
                }
                
                this.scrollToBottom();
            }

            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
        }

        // Initialize the chat interface when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new KoraChatInterface();
        });
    </script>
</body>
</html>
