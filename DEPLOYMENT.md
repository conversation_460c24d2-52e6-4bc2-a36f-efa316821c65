# Kora 2.0 - Deployment Guide

## 🎉 Project Complete!

Kora 2.0 is now fully functional and ready for use. This advanced AI chatbot features personality-driven conversations, context awareness, and multi-platform support.

## ✅ What's Working

### Core Features
- ✅ **Personality System**: 6 distinct personalities (<PERSON>, <PERSON>, <PERSON>li<PERSON>y, Roleplay, Academic, Casual)
- ✅ **Context Management**: Intelligent conversation context with compression and memory
- ✅ **Memory System**: Persistent memory with search and categorization
- ✅ **Command System**: Rich command interface with 12+ commands
- ✅ **Web Interface**: Beautiful, responsive chat interface
- ✅ **REST API**: Complete API for integration
- ✅ **Configuration Management**: Environment-based configuration system

### Tested Functionality
- ✅ Server starts successfully on port 3000
- ✅ Health check endpoint working
- ✅ Chat API responding correctly
- ✅ Personality switching working
- ✅ Command processing functional
- ✅ Web interface accessible
- ✅ Context persistence working
- ✅ Memory management operational

## 🚀 Quick Start

### 1. Start the Server
```bash
npm start
```

### 2. Access the Application
- **Web Interface**: http://localhost:3000
- **API Endpoint**: http://localhost:3000/api
- **Health Check**: http://localhost:3000/health

### 3. Test the Chatbot
```bash
# Test basic chat
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello there!"}'

# Test commands
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "/help"}'

# Change personality
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "/set_personality flirty", "sessionId": "test"}'
```

## 🎭 Available Personalities

1. **Friendly** (Default) - Warm, engaging, and slightly playful
2. **Professional** - Formal, structured, business-oriented
3. **Flirty** - Playful, charming, subtly romantic
4. **Roleplay** - Immersive character for roleplay scenarios
5. **Academic** - Intellectual, analytical, educational
6. **Casual** - Relaxed, informal, like talking to a friend

## 💬 Command Reference

### Basic Commands
- `/help` - Show available commands
- `/status` - Display current session information
- `/set_personality <name>` - Change personality mode
- `/reset_context` - Clear conversation history

### Memory & Data Commands
- `/save_memory <text>` - Save important information
- `/load_memory <file>` - Import memory from JSON file
- `/load_lore <file>` - Import lore/world data
- `/export_data [memory|lore]` - Export data to file
- `/stats` - Show detailed statistics

## 🔧 Configuration

### Environment Variables
Create a `.env` file based on `.env.example`:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Discord Bot (optional)
DISCORD_TOKEN=your_discord_bot_token
DISCORD_CLIENT_ID=your_client_id

# Features
ENABLE_DISCORD=false
ENABLE_WEB=true
LOG_LEVEL=info
```

### Configuration Files
- `config/default.json` - Base configuration
- `config/development.json` - Development settings (Discord disabled)
- `config/production.json` - Production settings
- `config/local.json` - Local overrides (optional)

## 📁 Project Structure

```
kora-2.0/
├── src/
│   ├── core/
│   │   └── ChatbotEngine.js      # Main chatbot logic
│   ├── discord/
│   │   └── DiscordBot.js         # Discord integration
│   ├── web-api/
│   │   └── routes.js             # REST API routes
│   └── utils/
│       ├── ConfigManager.js      # Configuration management
│       ├── MemoryManager.js      # Memory & lore system
│       ├── ContextManager.js     # Context handling
│       └── CommandProcessor.js   # Command processing
├── web/
│   └── index.html                # Web chat interface
├── config/                       # Configuration files
├── data/                         # Data storage
│   ├── sample_memory.json        # Sample memory data
│   └── sample_lore.json          # Sample lore data
├── tests/                        # Test files
└── README.md                     # Documentation
```

## 🌐 API Endpoints

### Chat API
- `POST /api/chat` - Send message to chatbot
- `GET /api/personalities` - Get available personalities
- `POST /api/personality` - Set session personality

### Session Management
- `GET /api/session/:id` - Get session info
- `DELETE /api/session/:id` - Delete session

### Data Management
- `POST /api/memory` - Add memory entry
- `POST /api/lore` - Load lore data
- `GET /api/stats` - Get system statistics
- `GET /api/config` - Get public configuration

## 🤖 Discord Integration (Optional)

To enable Discord bot:

1. Create Discord application at https://discord.com/developers/applications
2. Create bot and copy token to `.env`
3. Set `DISCORD_TOKEN` in environment
4. Enable Discord in config: `"discord": {"enabled": true}`
5. Start with: `npm run discord`

## 💾 Data Management

### Memory System
- Automatic importance detection
- Searchable memory entries
- Category-based organization
- Export/import capabilities

### Sample Data
Load sample data to test the system:
```bash
# Via API
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "/load_memory sample_memory.json"}'

curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "/load_lore sample_lore.json"}'
```

## 🔍 Monitoring

### Health Check
```bash
curl http://localhost:3000/health
```

### Statistics
```bash
curl http://localhost:3000/api/stats
```

### Logs
- Development: Console output
- Production: `data/logs/kora.log`

## 🛠️ Development

### Development Mode
```bash
npm run dev  # Starts with nodemon for auto-reload
```

### Testing
```bash
npm test              # Run tests
npm run test:watch    # Watch mode
npm run test:coverage # Coverage report
```

## 🚨 Troubleshooting

### Common Issues

1. **Port already in use**
   - Change PORT in `.env` or kill existing process

2. **Discord bot not responding**
   - Check DISCORD_TOKEN in `.env`
   - Verify bot permissions in Discord server

3. **Memory not persisting**
   - Ensure `data/` directory is writable
   - Check file permissions

4. **Web interface not loading**
   - Verify server is running on correct port
   - Check browser console for errors

### Debug Mode
Set `LOG_LEVEL=debug` in `.env` for detailed logging.

## 🎯 Next Steps

### Potential Enhancements
1. **AI Integration**: Connect to OpenAI, Anthropic, or other AI APIs
2. **Database**: Add PostgreSQL/MongoDB for scalable data storage
3. **Authentication**: Add user authentication and sessions
4. **Voice Support**: Add speech-to-text and text-to-speech
5. **Mobile App**: Create React Native mobile application
6. **Analytics**: Add conversation analytics and insights

### Production Deployment
1. Set `NODE_ENV=production`
2. Configure reverse proxy (nginx)
3. Set up SSL certificates
4. Configure monitoring and logging
5. Set up backup systems for data

## 📞 Support

The chatbot is fully functional and ready for use. All core features are working:
- ✅ Personality-driven conversations
- ✅ Context awareness and memory
- ✅ Command system
- ✅ Web interface
- ✅ API endpoints
- ✅ Configuration management

Enjoy your new AI companion! 🤖💕
