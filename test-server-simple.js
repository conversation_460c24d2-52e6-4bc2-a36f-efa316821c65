import express from 'express';
import cors from 'cors';
import ChatbotEngine from './src/core/ChatbotEngine.js';
import ConfigManager from './src/utils/ConfigManager.js';

async function startTestServer() {
  console.log('🧪 Iniciando servidor de teste...');
  
  try {
    // Load configuration
    const config = ConfigManager.getAll();
    
    // Initialize chatbot engine
    const chatbotConfig = {
      personalities: config.personalities || {},
      defaultPersonality: config.chatbot?.defaultPersonality || 'friendly',
      contextWindow: config.chatbot?.contextWindow || 10,
      maxMemoryEntries: config.chatbot?.maxMemoryEntries || 100,
      commands: config.commands || {},
      ai: config.chatbot?.ai || {}
    };
    
    console.log('🤖 Inicializando ChatbotEngine...');
    const chatbot = new ChatbotEngine(chatbotConfig);
    
    // Wait for initialization
    await new Promise((resolve, reject) => {
      chatbot.once('initialized', resolve);
      chatbot.once('error', reject);
      setTimeout(() => reject(new Error('Timeout')), 10000);
    });
    
    console.log('✅ ChatbotEngine inicializado');
    
    // Create Express app
    const app = express();
    
    // Basic middleware
    app.use(cors());
    app.use(express.json());
    
    // Simple chat endpoint
    app.post('/api/chat', async (req, res) => {
      console.log('📨 Recebida requisição de chat:', req.body);
      
      try {
        const { message } = req.body;
        
        if (!message) {
          return res.status(400).json({ error: 'Message required' });
        }
        
        console.log('🔄 Processando mensagem...');
        const response = await chatbot.processMessage(
          message,
          'test-session',
          'web',
          {}
        );
        
        console.log('✅ Resposta gerada:', response.content.substring(0, 50) + '...');
        
        res.json({
          success: true,
          response: response.content,
          personality: response.personality
        });
        
      } catch (error) {
        console.error('❌ Erro no chat:', error);
        res.status(500).json({ error: error.message });
      }
    });
    
    // Health check
    app.get('/health', (req, res) => {
      res.json({ status: 'ok' });
    });
    
    // Start server
    const PORT = 3001;
    app.listen(PORT, () => {
      console.log(`✅ Servidor de teste rodando na porta ${PORT}`);
      console.log(`🌐 Teste: http://localhost:${PORT}/health`);
      console.log(`💬 Chat: POST http://localhost:${PORT}/api/chat`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao iniciar servidor:', error);
  }
}

startTestServer();
