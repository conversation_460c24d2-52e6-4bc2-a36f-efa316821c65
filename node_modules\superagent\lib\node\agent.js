"use strict";

/**
 * Module dependencies.
 */

const {
  <PERSON>ieJar
} = require('cookiejar');
const {
  CookieAccessInfo
} = require('cookiejar');
const methods = require('methods');
const request = require('../..');
const AgentBase = require('../agent-base');

/**
 * Initialize a new `Agent`.
 *
 * @api public
 */

class Agent extends AgentBase {
  constructor(options) {
    super();
    this.jar = new CookieJar();
    if (options) {
      if (options.ca) {
        this.ca(options.ca);
      }
      if (options.key) {
        this.key(options.key);
      }
      if (options.pfx) {
        this.pfx(options.pfx);
      }
      if (options.cert) {
        this.cert(options.cert);
      }
      if (options.rejectUnauthorized === false) {
        this.disableTLSCerts();
      }
    }
  }

  /**
   * Save the cookies in the given `res` to
   * the agent's cookie jar for persistence.
   *
   * @param {Response} res
   * @api private
   */
  _saveCookies(res) {
    const cookies = res.headers['set-cookie'];
    if (cookies) {
      var _res$request;
      const url = new URL(((_res$request = res.request) === null || _res$request === void 0 ? void 0 : _res$request.url) || '');
      this.jar.setCookies(cookies, url.hostname, url.pathname);
    }
  }

  /**
   * Attach cookies when available to the given `req`.
   *
   * @param {Request} req
   * @api private
   */
  _attachCookies(request_) {
    const url = new URL(request_.url);
    const access = new CookieAccessInfo(url.hostname, url.pathname, url.protocol === 'https:');
    const cookies = this.jar.getCookies(access).toValueString();
    request_.cookies = cookies;
  }
}
for (const name of methods) {
  const method = name.toUpperCase();
  Agent.prototype[name] = function (url, fn) {
    const request_ = new request.Request(method, url);
    request_.on('response', this._saveCookies.bind(this));
    request_.on('redirect', this._saveCookies.bind(this));
    request_.on('redirect', this._attachCookies.bind(this, request_));
    this._setDefaults(request_);
    this._attachCookies(request_);
    if (fn) {
      request_.end(fn);
    }
    return request_;
  };
}
Agent.prototype.del = Agent.prototype.delete;

// create a Proxy that can instantiate a new Agent without using `new` keyword
// (for backward compatibility and chaining)
const proxyAgent = new Proxy(Agent, {
  apply(target, thisArg, argumentsList) {
    return new target(...argumentsList);
  }
});
module.exports = proxyAgent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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