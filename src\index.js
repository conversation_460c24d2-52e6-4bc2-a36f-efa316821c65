import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import fs from 'fs-extra';
import path from 'path';

import ChatbotEngine from './core/ChatbotEngine.js';
import { createWebAPI } from './web-api/routes.js';

// Load environment variables
dotenv.config();

// Load configuration
const configPath = path.join(process.cwd(), 'config', 'default.json');
const config = await fs.readJson(configPath);

// Initialize the chatbot engine
const chatbot = new ChatbotEngine(config.chatbot);

// Create Express app
const app = express();

// Middleware
app.use(helmet());
app.use(morgan('combined'));
app.use(cors(config.web.cors));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// API Routes
app.use('/api', createWebAPI(chatbot, config));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Serve static files for web interface
const webPath = path.join(process.cwd(), 'web');
const webDistPath = path.join(webPath, 'dist');

// Check for built version first, then fallback to development version
if (await fs.pathExists(webDistPath)) {
  app.use(express.static(webDistPath));
  app.get('*', (req, res) => {
    if (!req.path.startsWith('/api')) {
      res.sendFile(path.join(webDistPath, 'index.html'));
    }
  });
} else if (await fs.pathExists(webPath)) {
  app.use(express.static(webPath));
  app.get('*', (req, res) => {
    if (!req.path.startsWith('/api')) {
      res.sendFile(path.join(webPath, 'index.html'));
    }
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
const PORT = process.env.PORT || config.server.port || 3000;
const HOST = process.env.HOST || config.server.host || 'localhost';

app.listen(PORT, HOST, () => {
  console.log(`🤖 Kora 2.0 Chatbot Server running on http://${HOST}:${PORT}`);
  console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
  console.log(`🌐 API endpoint: http://${HOST}:${PORT}/api`);
  
  if (await fs.pathExists(webDistPath) || await fs.pathExists(webPath)) {
    console.log(`🎨 Web interface: http://${HOST}:${PORT}`);
  } else {
    console.log(`🎨 Web interface: Not available (web directory not found)`);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

// Clean up old sessions periodically
setInterval(() => {
  chatbot.cleanupSessions();
}, 60 * 60 * 1000); // Every hour

export { chatbot, app };
