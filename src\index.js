import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import fs from 'fs-extra';
import path from 'path';

import ChatbotEngine from './core/ChatbotEngine.js';
import { createWebAPI } from './web-api/routes.js';
import ConfigManager from './utils/ConfigManager.js';

async function startServer() {
  // Load configuration
  const config = ConfigManager.getAll();

  // Initialize the chatbot engine
  const chatbotConfig = {
    personalities: config.personalities || {},
    defaultPersonality: config.chatbot?.defaultPersonality || config.defaultPersonality || 'friendly',
    contextWindow: config.chatbot?.contextWindow || config.contextWindow || 10,
    maxMemoryEntries: config.chatbot?.maxMemoryEntries || config.maxMemoryEntries || 100,
    commands: config.commands || {},
    ai: config.chatbot?.ai || config.ai || {}
  };
  const chatbot = new ChatbotEngine(chatbotConfig);

  // Create Express app
  const app = express();

  // Middleware (simplified)
  app.use(cors());
  app.use(express.json());

  // API Routes
  app.use('/api', createWebAPI(chatbot, chatbotConfig));

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });

  // Serve static files for web interface
  const webPath = path.join(process.cwd(), 'web');
  const webDistPath = path.join(webPath, 'dist');

  // Check for built version first, then fallback to development version
  const hasDistPath = await fs.pathExists(webDistPath);
  const hasWebPath = await fs.pathExists(webPath);

  if (hasDistPath) {
    app.use(express.static(webDistPath));
    app.get('*', (req, res) => {
      if (!req.path.startsWith('/api')) {
        res.sendFile(path.join(webDistPath, 'index.html'));
      }
    });
  } else if (hasWebPath) {
    app.use(express.static(webPath));
    app.get('*', (req, res) => {
      if (!req.path.startsWith('/api')) {
        res.sendFile(path.join(webPath, 'index.html'));
      }
    });
  }

  // Error handling middleware
  app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
  });

  // Validate configuration
  const validation = ConfigManager.validate();
  if (!validation.isValid) {
    console.error('Configuration validation failed:');
    validation.errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }

  // Log configuration summary
  console.log('🔧 Configuration Summary:', JSON.stringify(ConfigManager.getSummary(), null, 2));

  // Start server
  const serverConfig = ConfigManager.getServerConfig();
  const PORT = serverConfig.port;
  const HOST = serverConfig.host;

  app.listen(PORT, HOST, () => {
    console.log(`🤖 Kora 2.0 Chatbot Server running on http://${HOST}:${PORT}`);
    console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
    console.log(`🌐 API endpoint: http://${HOST}:${PORT}/api`);

    if (hasDistPath || hasWebPath) {
      console.log(`🎨 Web interface: http://${HOST}:${PORT}`);
    } else {
      console.log(`🎨 Web interface: Not available (web directory not found)`);
    }
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    process.exit(0);
  });

  process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully...');
    process.exit(0);
  });

  // Clean up old sessions periodically
  setInterval(() => {
    chatbot.cleanupSessions();
  }, 60 * 60 * 1000); // Every hour

  return { chatbot, app };
}

// Start the server
startServer().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
