import { Client, GatewayIntentBits } from 'discord.js';
import dotenv from 'dotenv';

dotenv.config();

async function testDiscord() {
  console.log('🧪 Testando Discord Bot...');
  
  const token = process.env.DISCORD_TOKEN;
  console.log('🔑 Token Discord:', token ? `${token.substring(0, 10)}...` : 'NÃO ENCONTRADO');
  
  if (!token) {
    console.error('❌ DISCORD_TOKEN não encontrado no .env');
    return;
  }
  
  try {
    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
      ]
    });
    
    client.once('ready', () => {
      console.log('✅ Discord Bot conectado como:', client.user.tag);
      console.log('🏠 Servidores:', client.guilds.cache.size);
      client.destroy();
      process.exit(0);
    });
    
    client.on('error', (error) => {
      console.error('❌ Erro do Discord:', error);
      process.exit(1);
    });
    
    console.log('🔄 Conectando...');
    await client.login(token);
    
  } catch (error) {
    console.error('❌ Erro ao conectar:', error.message);
    process.exit(1);
  }
}

testDiscord();
