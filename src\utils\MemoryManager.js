import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * Memory Manager - Handles persistent memory and lore storage
 */
class MemoryManager {
  constructor(dataDir = './data') {
    this.dataDir = dataDir;
    this.memoryFile = path.join(dataDir, 'memory.json');
    this.loreFile = path.join(dataDir, 'lore.json');
    this.sessionsFile = path.join(dataDir, 'sessions.json');
    
    this.memory = [];
    this.lore = new Map();
    this.sessions = new Map();
    
    this.initialize();
  }

  async initialize() {
    try {
      await fs.ensureDir(this.dataDir);
      await this.loadMemory();
      await this.loadLore();
      await this.loadSessions();
    } catch (error) {
      console.error('Failed to initialize MemoryManager:', error);
    }
  }

  /**
   * Memory Management
   */
  async loadMemory() {
    try {
      if (await fs.pathExists(this.memoryFile)) {
        this.memory = await fs.readJson(this.memoryFile);
      }
    } catch (error) {
      console.warn('Could not load memory file:', error.message);
      this.memory = [];
    }
  }

  async saveMemory() {
    try {
      await fs.writeJson(this.memoryFile, this.memory, { spaces: 2 });
    } catch (error) {
      console.error('Could not save memory file:', error);
    }
  }

  addMemoryEntry(content, type = 'user', metadata = {}) {
    const entry = {
      id: uuidv4(),
      content,
      type,
      metadata,
      timestamp: new Date().toISOString(),
      importance: metadata.importance || 1
    };
    
    this.memory.push(entry);
    this.saveMemory();
    return entry;
  }

  searchMemory(query, limit = 10) {
    const queryLower = query.toLowerCase();
    return this.memory
      .filter(entry => 
        entry.content.toLowerCase().includes(queryLower) ||
        (entry.metadata.tags && entry.metadata.tags.some(tag => 
          tag.toLowerCase().includes(queryLower)
        ))
      )
      .sort((a, b) => {
        // Sort by importance and recency
        const importanceDiff = b.importance - a.importance;
        if (importanceDiff !== 0) return importanceDiff;
        return new Date(b.timestamp) - new Date(a.timestamp);
      })
      .slice(0, limit);
  }

  getRecentMemory(limit = 20) {
    return this.memory
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
  }

  /**
   * Lore Management
   */
  async loadLore() {
    try {
      if (await fs.pathExists(this.loreFile)) {
        const loreData = await fs.readJson(this.loreFile);
        this.lore = new Map(Object.entries(loreData));
      }
    } catch (error) {
      console.warn('Could not load lore file:', error.message);
      this.lore = new Map();
    }
  }

  async saveLore() {
    try {
      const loreObject = Object.fromEntries(this.lore);
      await fs.writeJson(this.loreFile, loreObject, { spaces: 2 });
    } catch (error) {
      console.error('Could not save lore file:', error);
    }
  }

  addLoreEntry(key, content, category = 'general') {
    const entry = {
      id: uuidv4(),
      key,
      content,
      category,
      timestamp: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      accessCount: 0
    };
    
    this.lore.set(key, entry);
    this.saveLore();
    return entry;
  }

  getLoreEntry(key) {
    const entry = this.lore.get(key);
    if (entry) {
      entry.lastAccessed = new Date().toISOString();
      entry.accessCount++;
      this.saveLore();
    }
    return entry;
  }

  searchLore(query, category = null) {
    const queryLower = query.toLowerCase();
    const results = [];
    
    for (const [key, entry] of this.lore.entries()) {
      if (category && entry.category !== category) continue;
      
      if (
        key.toLowerCase().includes(queryLower) ||
        entry.content.toLowerCase().includes(queryLower)
      ) {
        results.push(entry);
      }
    }
    
    return results.sort((a, b) => b.accessCount - a.accessCount);
  }

  getLoreByCategory(category) {
    const results = [];
    for (const entry of this.lore.values()) {
      if (entry.category === category) {
        results.push(entry);
      }
    }
    return results;
  }

  /**
   * Session Management
   */
  async loadSessions() {
    try {
      if (await fs.pathExists(this.sessionsFile)) {
        const sessionsData = await fs.readJson(this.sessionsFile);
        this.sessions = new Map(Object.entries(sessionsData));
      }
    } catch (error) {
      console.warn('Could not load sessions file:', error.message);
      this.sessions = new Map();
    }
  }

  async saveSessions() {
    try {
      const sessionsObject = Object.fromEntries(this.sessions);
      await fs.writeJson(this.sessionsFile, sessionsObject, { spaces: 2 });
    } catch (error) {
      console.error('Could not save sessions file:', error);
    }
  }

  saveSession(sessionId, sessionData) {
    const session = {
      id: sessionId,
      ...sessionData,
      lastSaved: new Date().toISOString()
    };
    
    this.sessions.set(sessionId, session);
    this.saveSessions();
    return session;
  }

  loadSession(sessionId) {
    return this.sessions.get(sessionId);
  }

  deleteSession(sessionId) {
    const deleted = this.sessions.delete(sessionId);
    if (deleted) {
      this.saveSessions();
    }
    return deleted;
  }

  cleanupOldSessions(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days default
    const now = new Date();
    let cleaned = 0;
    
    for (const [sessionId, session] of this.sessions.entries()) {
      const lastActivity = new Date(session.lastActivity || session.lastSaved);
      if (now - lastActivity > maxAge) {
        this.sessions.delete(sessionId);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      this.saveSessions();
    }
    
    return cleaned;
  }

  /**
   * Import/Export Functions
   */
  async importMemoryFromFile(filePath) {
    try {
      const data = await fs.readJson(filePath);
      
      if (Array.isArray(data)) {
        // Import as memory entries
        for (const item of data) {
          if (typeof item === 'string') {
            this.addMemoryEntry(item, 'imported');
          } else if (item.content) {
            this.addMemoryEntry(item.content, 'imported', item.metadata || {});
          }
        }
      } else if (data.memory) {
        // Import structured data
        for (const entry of data.memory) {
          this.addMemoryEntry(entry.content, entry.type || 'imported', entry.metadata || {});
        }
      }
      
      return true;
    } catch (error) {
      console.error('Failed to import memory from file:', error);
      return false;
    }
  }

  async importLoreFromFile(filePath) {
    try {
      const data = await fs.readJson(filePath);
      
      if (typeof data === 'object') {
        for (const [key, value] of Object.entries(data)) {
          if (typeof value === 'string') {
            this.addLoreEntry(key, value, 'imported');
          } else if (value.content) {
            this.addLoreEntry(key, value.content, value.category || 'imported');
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('Failed to import lore from file:', error);
      return false;
    }
  }

  async exportMemory(filePath = null) {
    const exportData = {
      exportDate: new Date().toISOString(),
      memory: this.memory,
      totalEntries: this.memory.length
    };
    
    if (filePath) {
      await fs.writeJson(filePath, exportData, { spaces: 2 });
    }
    
    return exportData;
  }

  async exportLore(filePath = null) {
    const exportData = {
      exportDate: new Date().toISOString(),
      lore: Object.fromEntries(this.lore),
      totalEntries: this.lore.size
    };
    
    if (filePath) {
      await fs.writeJson(filePath, exportData, { spaces: 2 });
    }
    
    return exportData;
  }

  /**
   * Statistics and Analytics
   */
  getMemoryStats() {
    const typeCount = {};
    const categoryCount = {};
    
    for (const entry of this.memory) {
      typeCount[entry.type] = (typeCount[entry.type] || 0) + 1;
      const category = entry.metadata?.category || 'uncategorized';
      categoryCount[category] = (categoryCount[category] || 0) + 1;
    }
    
    return {
      totalEntries: this.memory.length,
      typeDistribution: typeCount,
      categoryDistribution: categoryCount,
      oldestEntry: this.memory.length > 0 ? 
        Math.min(...this.memory.map(e => new Date(e.timestamp))) : null,
      newestEntry: this.memory.length > 0 ? 
        Math.max(...this.memory.map(e => new Date(e.timestamp))) : null
    };
  }

  getLoreStats() {
    const categoryCount = {};
    let totalAccess = 0;
    
    for (const entry of this.lore.values()) {
      categoryCount[entry.category] = (categoryCount[entry.category] || 0) + 1;
      totalAccess += entry.accessCount;
    }
    
    return {
      totalEntries: this.lore.size,
      categoryDistribution: categoryCount,
      totalAccesses: totalAccess,
      averageAccesses: this.lore.size > 0 ? totalAccess / this.lore.size : 0
    };
  }
}

export default MemoryManager;
