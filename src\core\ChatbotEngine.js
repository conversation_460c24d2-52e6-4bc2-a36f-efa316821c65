import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs-extra';
import path from 'path';
import MemoryManager from '../utils/MemoryManager.js';
import ContextManager from '../utils/ContextManager.js';
import CommandProcessor from '../utils/CommandProcessor.js';

/**
 * Core Chatbot Engine - Handles personality-driven conversations
 * with context awareness and memory integration
 */
class ChatbotEngine extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.personalities = config.personalities || {};
    this.currentPersonality = config.defaultPersonality || 'friendly';
    this.contextWindow = config.contextWindow || 10;
    this.maxMemoryEntries = config.maxMemoryEntries || 100;

    // Session storage for active conversations
    this.sessions = new Map();

    // Initialize managers
    this.memoryManager = new MemoryManager('./data');
    this.contextManager = new ContextManager({
      maxContextLength: this.contextWindow,
      maxTokens: 4000
    });
    this.commandProcessor = new CommandProcessor(this, config.commands || {});

    this.initialize();
  }

  async initialize() {
    try {
      await this.memoryManager.initialize();
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize ChatbotEngine:', error);
      this.emit('error', error);
    }
  }

  /**
   * Process a message and generate a response
   */
  async processMessage(message, sessionId, platform = 'web', userSettings = {}) {
    try {
      // Get or create session
      const session = this.getOrCreateSession(sessionId, userSettings);
      
      // Add message to context using context manager
      this.contextManager.addMessageToContext(session, {
        role: 'user',
        content: message,
        platform,
        metadata: { userSettings }
      });

      // Process context
      this.contextManager.processContext(session);

      // Check for commands
      const commandResult = await this.commandProcessor.processCommand(message, session);
      if (commandResult) {
        return commandResult;
      }

      // Generate response based on personality and context
      const response = await this.generateResponse(session, platform);
      
      // Add response to context using context manager
      this.contextManager.addMessageToContext(session, {
        role: 'assistant',
        content: response,
        platform,
        metadata: { personality: session.personality }
      });

      // Update session activity
      session.lastActivity = new Date();

      return {
        content: response,
        personality: session.personality,
        sessionId: sessionId,
        platform: platform
      };

    } catch (error) {
      console.error('Error processing message:', error);
      return {
        content: "I apologize, but I encountered an error processing your message. Please try again.",
        error: true
      };
    }
  }

  /**
   * Get or create a conversation session
   */
  getOrCreateSession(sessionId, userSettings = {}) {
    if (!this.sessions.has(sessionId)) {
      const session = {
        id: sessionId,
        personality: userSettings.personality || this.currentPersonality,
        context: [],
        memory: [],
        lore: userSettings.lore || [],
        settings: userSettings,
        createdAt: new Date(),
        lastActivity: new Date()
      };
      this.sessions.set(sessionId, session);
    }
    return this.sessions.get(sessionId);
  }

  /**
   * Generate response based on personality and context
   */
  async generateResponse(session, platform) {
    const personality = this.personalities[session.personality] || this.personalities.friendly;

    // Get relevant context using context manager
    const lastMessage = session.context[session.context.length - 1];
    const relevantContext = this.contextManager.getRelevantContext(
      session,
      lastMessage.content,
      this.memoryManager
    );

    // Analyze mood for tone adjustment
    const moodAnalysis = this.contextManager.analyzeMood(session);

    // Generate response with enhanced context
    const response = this.generateRuleBasedResponse(
      session,
      personality,
      platform,
      relevantContext,
      moodAnalysis
    );

    return this.formatResponseForPlatform(response, platform);
  }

  /**
   * Generate a rule-based response (placeholder for AI integration)
   */
  generateRuleBasedResponse(session, personality, platform, relevantContext, moodAnalysis) {
    const lastMessage = session.context[session.context.length - 1];
    const messageContent = lastMessage.content.toLowerCase();

    // Use mood analysis for sentiment
    const sentiment = moodAnalysis.mood;

    // Check for memory references
    const hasMemoryContext = relevantContext.memory.length > 0;
    const hasLoreContext = relevantContext.lore.length > 0;

    // Generate response based on personality traits and context
    let response = "";

    if (messageContent.includes('hello') || messageContent.includes('hi')) {
      response = this.generateGreeting(personality, sentiment, hasMemoryContext);
    } else if (messageContent.includes('how are you')) {
      response = this.generateStatusResponse(personality, moodAnalysis);
    } else if (messageContent.includes('help')) {
      response = this.generateHelpResponse(personality);
    } else if (messageContent.includes('remember') || messageContent.includes('recall')) {
      response = this.generateMemoryResponse(personality, relevantContext);
    } else {
      response = this.generateContextualResponse(session, personality, sentiment, relevantContext);
    }

    return response;
  }

  /**
   * Simple sentiment analysis
   */
  analyzeSentiment(text) {
    const positiveWords = ['happy', 'good', 'great', 'awesome', 'love', 'excited'];
    const negativeWords = ['sad', 'bad', 'terrible', 'hate', 'angry', 'frustrated'];
    
    const words = text.split(' ');
    let score = 0;
    
    words.forEach(word => {
      if (positiveWords.includes(word)) score += 1;
      if (negativeWords.includes(word)) score -= 1;
    });
    
    if (score > 0) return 'positive';
    if (score < 0) return 'negative';
    return 'neutral';
  }

  /**
   * Generate greeting based on personality
   */
  generateGreeting(personality, sentiment, hasMemoryContext = false) {
    const personalityKey = personality.name.toLowerCase();

    let greetings = {
      friendly: ["Hey there! 😊 How's it going?", "Hello! Great to see you!", "Hi! What's on your mind today?"],
      professional: ["Good day. How may I assist you?", "Hello. What can I help you with today?"],
      flirty: ["Well hello there, gorgeous! 😉", "Hey cutie! What brings you here?"],
      roleplay: ["*looks up with interest* Oh, hello there!"]
    };

    // Add memory-aware greetings if we have context
    if (hasMemoryContext) {
      greetings.friendly.push("Hey! Good to see you again! 😊");
      greetings.professional.push("Welcome back. How may I assist you today?");
      greetings.flirty.push("Well well, look who's back! 😘");
      greetings.roleplay.push("*smiles warmly* Welcome back, friend!");
    }

    const options = greetings[personalityKey] || greetings.friendly;
    return options[Math.floor(Math.random() * options.length)];
  }

  /**
   * Generate status response
   */
  generateStatusResponse(personality, moodAnalysis) {
    const personalityKey = personality.name.toLowerCase();

    let responses = {
      friendly: ["I'm doing great, thanks for asking! How about you?", "Fantastic! Ready to chat and help out!"],
      professional: ["I am functioning optimally and ready to assist.", "All systems operational. How may I help?"],
      flirty: ["I'm doing wonderful now that you're here! 💕", "Better now that I'm talking to you!"],
      roleplay: ["*stretches and smiles* I'm doing well, thank you for asking!"]
    };

    // Adjust response based on user's mood
    if (moodAnalysis.mood === 'negative') {
      responses.friendly.push("I'm here and ready to listen if you need to talk.");
      responses.professional.push("I am available to provide support and assistance.");
      responses.flirty.push("I'm here to brighten your day! 💕");
      responses.roleplay.push("*offers a comforting presence* I'm here for you.");
    }

    const options = responses[personalityKey] || responses.friendly;
    return options[Math.floor(Math.random() * options.length)];
  }

  /**
   * Generate help response
   */
  generateHelpResponse(personality) {
    return `I'm here to chat and help! You can:
• Just talk to me naturally - I'll adapt to your mood and style
• Use commands like /set_personality to change my personality
• Ask me questions or share what's on your mind
• I remember our conversation context and important details
• I can recall previous conversations and learn from them

What would you like to talk about?`;
  }

  /**
   * Generate memory-based response
   */
  generateMemoryResponse(personality, relevantContext) {
    const personalityKey = personality.name.toLowerCase();

    if (relevantContext.memory.length === 0) {
      const noMemoryResponses = {
        friendly: "I don't have any specific memories about that topic, but I'm always learning!",
        professional: "I do not have relevant information stored about that topic.",
        flirty: "Hmm, that doesn't ring a bell, but tell me more! 😊",
        roleplay: "*searches memory* I don't recall anything about that specifically."
      };
      return noMemoryResponses[personalityKey] || noMemoryResponses.friendly;
    }

    const memoryContent = relevantContext.memory[0].content;
    const responses = {
      friendly: `I remember something about that! ${memoryContent}`,
      professional: `According to my records: ${memoryContent}`,
      flirty: `Oh yes, I remember! ${memoryContent} 😊`,
      roleplay: `*recalls* Ah yes, ${memoryContent}`
    };

    return responses[personalityKey] || responses.friendly;
  }

  /**
   * Generate contextual response based on conversation history
   */
  generateContextualResponse(session, personality, sentiment, relevantContext) {
    const personalityKey = personality.name.toLowerCase();

    // Enhanced responses with context awareness
    const responses = {
      positive: {
        friendly: ["That's awesome! Tell me more!", "I love your enthusiasm!", "That sounds really great!"],
        professional: ["That is excellent news.", "I'm pleased to hear that.", "That sounds very positive."],
        flirty: ["You're so exciting! 😍", "I love your energy!", "You always know how to make me smile!"],
        roleplay: ["*eyes light up with interest* That sounds fascinating!"]
      },
      negative: {
        friendly: ["I'm sorry to hear that. Want to talk about it?", "That sounds tough. I'm here if you need to vent."],
        professional: ["I understand this may be challenging. How can I assist?", "I acknowledge your concerns."],
        flirty: ["Aww, let me cheer you up! 💕", "Don't worry, I'm here for you!"],
        roleplay: ["*offers a comforting presence* I'm here to listen."]
      },
      neutral: {
        friendly: ["Interesting! What do you think about that?", "I see! Can you tell me more?"],
        professional: ["Please provide additional details.", "I would like to understand better."],
        flirty: ["Mmm, tell me more about that! 😊", "You've got my attention!"],
        roleplay: ["*nods thoughtfully* Go on..."]
      }
    };

    // Add context-aware responses if we have relevant context
    if (relevantContext.relevant.length > 0) {
      responses.neutral.friendly.push("That reminds me of what we discussed earlier!");
      responses.neutral.professional.push("This relates to our previous conversation.");
      responses.neutral.flirty.push("Oh, like we were talking about before! 😊");
      responses.neutral.roleplay.push("*connects the dots* Ah, this ties into our earlier discussion.");
    }

    const sentimentResponses = responses[sentiment] || responses.neutral;
    const options = sentimentResponses[personalityKey] || sentimentResponses.friendly;

    return options[Math.floor(Math.random() * options.length)];
  }

  /**
   * Format response for specific platform
   */
  formatResponseForPlatform(response, platform) {
    if (platform === 'discord') {
      // Discord formatting with Markdown and length limits
      if (response.length > 1500) {
        return response.substring(0, 1497) + '...';
      }
      return response;
    }
    
    // Web platform - no special formatting needed
    return response;
  }





  /**
   * Add memory entry
   */
  addMemory(entry, type = 'user_added', metadata = {}) {
    return this.memoryManager.addMemoryEntry(entry, type, metadata);
  }

  /**
   * Search memory
   */
  searchMemory(query, limit = 10) {
    return this.memoryManager.searchMemory(query, limit);
  }

  /**
   * Add lore entry
   */
  addLore(key, content, category = 'general') {
    return this.memoryManager.addLoreEntry(key, content, category);
  }

  /**
   * Search lore
   */
  searchLore(query, category = null) {
    return this.memoryManager.searchLore(query, category);
  }

  /**
   * Set personality for all new sessions
   */
  setDefaultPersonality(personalityName) {
    if (this.personalities[personalityName]) {
      this.currentPersonality = personalityName;
    }
  }

  /**
   * Get session info
   */
  getSessionInfo(sessionId) {
    return this.sessions.get(sessionId);
  }

  /**
   * Clean up old sessions
   */
  cleanupSessions(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    const now = new Date();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity > maxAge) {
        this.sessions.delete(sessionId);
      }
    }
  }
}

export default ChatbotEngine;
