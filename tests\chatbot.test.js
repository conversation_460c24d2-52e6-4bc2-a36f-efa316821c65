import { jest } from '@jest/globals';
import ChatbotEngine from '../src/core/ChatbotEngine.js';

describe('ChatbotEngine', () => {
  let chatbot;
  const testConfig = {
    personalities: {
      friendly: {
        name: 'Friendly',
        description: 'A friendly personality',
        traits: ['warm', 'helpful'],
        systemPrompt: 'You are friendly'
      },
      professional: {
        name: 'Professional',
        description: 'A professional personality',
        traits: ['formal', 'precise'],
        systemPrompt: 'You are professional'
      }
    },
    defaultPersonality: 'friendly',
    contextWindow: 5,
    maxMemoryEntries: 10,
    commands: {
      enabled: true,
      prefix: '/',
      availableCommands: ['help', 'status', 'set_personality']
    }
  };

  beforeEach(() => {
    chatbot = new ChatbotEngine(testConfig);
  });

  afterEach(() => {
    // Clean up any resources
    if (chatbot) {
      chatbot.sessions.clear();
    }
  });

  describe('Initialization', () => {
    test('should initialize with default configuration', () => {
      expect(chatbot.personalities).toBeDefined();
      expect(chatbot.currentPersonality).toBe('friendly');
      expect(chatbot.sessions).toBeInstanceOf(Map);
    });

    test('should have memory and context managers', () => {
      expect(chatbot.memoryManager).toBeDefined();
      expect(chatbot.contextManager).toBeDefined();
      expect(chatbot.commandProcessor).toBeDefined();
    });
  });

  describe('Session Management', () => {
    test('should create new session', () => {
      const sessionId = 'test-session-1';
      const session = chatbot.getOrCreateSession(sessionId);
      
      expect(session).toBeDefined();
      expect(session.id).toBe(sessionId);
      expect(session.personality).toBe('friendly');
      expect(session.context).toEqual([]);
    });

    test('should return existing session', () => {
      const sessionId = 'test-session-2';
      const session1 = chatbot.getOrCreateSession(sessionId);
      const session2 = chatbot.getOrCreateSession(sessionId);
      
      expect(session1).toBe(session2);
    });

    test('should apply user settings to new session', () => {
      const sessionId = 'test-session-3';
      const userSettings = { personality: 'professional' };
      const session = chatbot.getOrCreateSession(sessionId, userSettings);
      
      expect(session.personality).toBe('professional');
    });
  });

  describe('Message Processing', () => {
    test('should process simple message', async () => {
      const sessionId = 'test-session-4';
      const message = 'Hello there!';
      
      const response = await chatbot.processMessage(message, sessionId);
      
      expect(response).toBeDefined();
      expect(response.content).toBeDefined();
      expect(response.sessionId).toBe(sessionId);
      expect(response.personality).toBe('friendly');
    });

    test('should handle command messages', async () => {
      const sessionId = 'test-session-5';
      const command = '/help';
      
      const response = await chatbot.processMessage(command, sessionId);
      
      expect(response).toBeDefined();
      expect(response.content).toContain('Available commands');
      expect(response.isCommand).toBe(true);
    });

    test('should maintain context across messages', async () => {
      const sessionId = 'test-session-6';
      
      await chatbot.processMessage('My name is John', sessionId);
      const session = chatbot.getSessionInfo(sessionId);
      
      expect(session.context).toHaveLength(2); // User message + bot response
      expect(session.context[0].content).toBe('My name is John');
      expect(session.context[0].role).toBe('user');
    });
  });

  describe('Personality System', () => {
    test('should change personality via command', async () => {
      const sessionId = 'test-session-7';
      const command = '/set_personality professional';
      
      const response = await chatbot.processMessage(command, sessionId);
      const session = chatbot.getSessionInfo(sessionId);
      
      expect(response.isCommand).toBe(true);
      expect(session.personality).toBe('professional');
      expect(response.content).toContain('Professional');
    });

    test('should reject invalid personality', async () => {
      const sessionId = 'test-session-8';
      const command = '/set_personality invalid';
      
      const response = await chatbot.processMessage(command, sessionId);
      
      expect(response.isCommand).toBe(true);
      expect(response.error).toBe(true);
      expect(response.content).toContain('not found');
    });
  });

  describe('Memory System', () => {
    test('should add memory entry', () => {
      const entry = chatbot.addMemory('Test memory entry');
      
      expect(entry).toBeDefined();
      expect(entry.id).toBeDefined();
      expect(entry.content).toBe('Test memory entry');
    });

    test('should search memory', () => {
      chatbot.addMemory('User likes pizza');
      chatbot.addMemory('User prefers tea over coffee');
      
      const results = chatbot.searchMemory('pizza');
      
      expect(results).toHaveLength(1);
      expect(results[0].content).toContain('pizza');
    });
  });

  describe('Context Management', () => {
    test('should trim context when too long', async () => {
      const sessionId = 'test-session-9';
      
      // Send multiple messages to exceed context window
      for (let i = 0; i < 10; i++) {
        await chatbot.processMessage(`Message ${i}`, sessionId);
      }
      
      const session = chatbot.getSessionInfo(sessionId);
      // Should not exceed contextWindow * 2 (10 in this case)
      expect(session.context.length).toBeLessThanOrEqual(10);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid session gracefully', () => {
      const session = chatbot.getSessionInfo('non-existent-session');
      expect(session).toBeUndefined();
    });

    test('should handle empty messages', async () => {
      const sessionId = 'test-session-10';
      const response = await chatbot.processMessage('', sessionId);
      
      expect(response).toBeDefined();
      expect(response.content).toBeDefined();
    });
  });

  describe('Cleanup', () => {
    test('should clean up old sessions', () => {
      const oldSessionId = 'old-session';
      const newSessionId = 'new-session';
      
      // Create sessions
      const oldSession = chatbot.getOrCreateSession(oldSessionId);
      const newSession = chatbot.getOrCreateSession(newSessionId);
      
      // Make old session appear old
      oldSession.lastActivity = new Date(Date.now() - 25 * 60 * 60 * 1000); // 25 hours ago
      
      // Clean up sessions older than 24 hours
      chatbot.cleanupSessions(24 * 60 * 60 * 1000);
      
      expect(chatbot.sessions.has(oldSessionId)).toBe(false);
      expect(chatbot.sessions.has(newSessionId)).toBe(true);
    });
  });
});

describe('Response Generation', () => {
  let chatbot;
  
  beforeEach(() => {
    chatbot = new ChatbotEngine({
      personalities: {
        friendly: {
          name: 'Friendly',
          description: 'A friendly personality',
          traits: ['warm', 'helpful'],
          systemPrompt: 'You are friendly'
        }
      },
      defaultPersonality: 'friendly'
    });
  });

  test('should generate greeting response', async () => {
    const sessionId = 'greeting-test';
    const response = await chatbot.processMessage('Hello', sessionId);
    
    expect(response.content.toLowerCase()).toMatch(/hello|hi|hey/);
  });

  test('should generate help response', async () => {
    const sessionId = 'help-test';
    const response = await chatbot.processMessage('help', sessionId);
    
    expect(response.content.toLowerCase()).toContain('help');
  });

  test('should adapt to user mood', async () => {
    const sessionId = 'mood-test';
    
    // Send a sad message
    const sadResponse = await chatbot.processMessage('I am feeling really sad today', sessionId);
    expect(sadResponse.content.toLowerCase()).toMatch(/sorry|here|listen|support/);
    
    // Send a happy message
    const happyResponse = await chatbot.processMessage('I am so excited and happy!', sessionId);
    expect(happyResponse.content.toLowerCase()).toMatch(/awesome|great|love|exciting/);
  });
});
