import ChatbotEngine from './src/core/ChatbotEngine.js';
import ConfigManager from './src/utils/ConfigManager.js';

async function testChatDirect() {
  console.log('🧪 Testando ChatbotEngine diretamente...');
  
  try {
    // Load configuration
    const config = ConfigManager.getAll();
    
    // Initialize chatbot engine
    const chatbotConfig = {
      personalities: config.personalities || {},
      defaultPersonality: config.chatbot?.defaultPersonality || 'friendly',
      contextWindow: config.chatbot?.contextWindow || 10,
      maxMemoryEntries: config.chatbot?.maxMemoryEntries || 100,
      commands: config.commands || {},
      ai: config.chatbot?.ai || {}
    };
    
    console.log('🤖 Criando ChatbotEngine...');
    const chatbot = new ChatbotEngine(chatbotConfig);
    
    console.log('⏳ Aguardando inicialização...');
    await new Promise((resolve, reject) => {
      chatbot.once('initialized', () => {
        console.log('✅ ChatbotEngine inicializado');
        resolve();
      });
      chatbot.once('error', reject);
      
      // Timeout
      setTimeout(() => {
        reject(new Error('Timeout na inicialização'));
      }, 10000);
    });
    
    console.log('💬 Testando processamento de mensagem...');
    const response = await chatbot.processMessage(
      'Olá! Como você está?',
      'test-session-123',
      'web',
      {}
    );
    
    console.log('✅ Resposta recebida:', response);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

testChatDirect();
