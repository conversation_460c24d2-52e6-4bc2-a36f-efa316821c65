import DiscordBot from './discord/DiscordBot.js';
import fs from 'fs-extra';
import path from 'path';

/**
 * Standalone Discord Bot Entry Point
 */
async function startDiscordBot() {
  try {
    console.log('🚀 Starting Kora 2.0 Discord Bot...');
    
    // Load configuration
    const configPath = path.join(process.cwd(), 'config', 'default.json');
    const config = await fs.readJson(configPath);
    
    // Check if Discord is enabled
    if (!config.discord.enabled) {
      console.log('❌ Discord bot is disabled in configuration');
      process.exit(1);
    }
    
    // Create and start bot
    const bot = new DiscordBot(config);
    await bot.start();
    
    // Graceful shutdown handlers
    process.on('SIGTERM', async () => {
      console.log('Received SIGTERM, shutting down Discord bot...');
      await bot.stop();
      process.exit(0);
    });
    
    process.on('SIGINT', async () => {
      console.log('Received SIGINT, shutting down Discord bot...');
      await bot.stop();
      process.exit(0);
    });
    
    // Keep the process alive
    console.log('✅ Discord bot is running. Press Ctrl+C to stop.');
    
  } catch (error) {
    console.error('❌ Failed to start Discord bot:', error);
    process.exit(1);
  }
}

// Start the bot
startDiscordBot();
