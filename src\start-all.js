import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import fs from 'fs-extra';
import path from 'path';

import ChatbotEngine from './core/ChatbotEngine.js';
import { createWebAPI } from './web-api/routes.js';
import ConfigManager from './utils/ConfigManager.js';
import DiscordBot from './discord/DiscordBot.js';

/**
 * Global variables for cross-function access
 */
let discordBot = null;

/**
 * Kora 2.0 - Unified Startup
 * Starts both Web Server and Discord Bot together
 */
async function startKora() {
  console.log('🚀 Starting Kora 2.0 - AI Chatbot System...\n');

  try {
    // Load configuration
    const config = ConfigManager.getAll();

    // Validate configuration
    const validation = ConfigManager.validate();
    if (!validation.isValid) {
      console.error('❌ Configuration validation failed:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
      process.exit(1);
    }

    // Log configuration summary
    console.log('🔧 Configuration Summary:', JSON.stringify(ConfigManager.getSummary(), null, 2));

    // Initialize shared chatbot engine
    const chatbotConfig = {
      personalities: config.personalities || {},
      defaultPersonality: config.chatbot?.defaultPersonality || config.defaultPersonality || 'friendly',
      contextWindow: config.chatbot?.contextWindow || config.contextWindow || 10,
      maxMemoryEntries: config.chatbot?.maxMemoryEntries || config.maxMemoryEntries || 100,
      commands: config.commands || {},
      ai: config.chatbot?.ai || config.ai || {}
    };

    console.log('\n🤖 Initializing Chatbot Engine...');
    const chatbot = new ChatbotEngine(chatbotConfig);

    // Wait for chatbot initialization
    await new Promise((resolve, reject) => {
      chatbot.once('initialized', resolve);
      chatbot.once('error', reject);

      // Timeout after 10 seconds
      setTimeout(() => {
        reject(new Error('Chatbot initialization timeout'));
      }, 10000);
    });

    console.log('✅ Chatbot Engine initialized successfully');

    // Start Web Server
    console.log('\n🌐 Starting Web Server...');
    await startWebServer(chatbot, config);

    // Start Discord Bot (if enabled)
    if (config.discord?.enabled && process.env.DISCORD_TOKEN) {
      console.log('\n🤖 Starting Discord Bot...');
      discordBot = await startDiscordBot(config, chatbot);
    } else {
      console.log('\n⚠️ Discord Bot disabled or token not provided');
      if (!config.discord?.enabled) {
        console.log('   - Discord is disabled in configuration');
      }
      if (!process.env.DISCORD_TOKEN) {
        console.log('   - DISCORD_TOKEN not found in environment');
      }
    }

    // Setup graceful shutdown
    setupGracefulShutdown();

    console.log('\n🎉 Kora 2.0 is fully operational!');
    console.log('📋 Services running:');
    console.log(`   🌐 Web Server: http://localhost:${config.server?.port || 3000}`);
    console.log(`   🤖 Discord Bot: ${config.discord?.enabled && process.env.DISCORD_TOKEN ? 'Online' : 'Offline'}`);
    console.log(`   🧠 AI Provider: ${chatbot.aiService?.getStatus()?.provider || 'fallback'}`);

  } catch (error) {
    console.error('❌ Failed to start Kora 2.0:', error);
    process.exit(1);
  }
}

/**
 * Start Web Server
 */
async function startWebServer(chatbot, config) {
  return new Promise((resolve, reject) => {
    try {
      // Create Express app
      const app = express();

      // Middleware
      app.use(cors(config.web?.cors || { origin: true, credentials: true }));
      app.use(express.json({ limit: '10mb' }));
      app.use(express.urlencoded({ extended: true }));

      // API Routes
      app.use('/api', createWebAPI(chatbot, {
        personalities: config.personalities || {},
        defaultPersonality: config.chatbot?.defaultPersonality || 'friendly',
        contextWindow: config.chatbot?.contextWindow || 10,
        commands: config.commands || {}
      }));

      // Health check endpoint
      app.get('/health', (req, res) => {
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          services: {
            web: 'online',
            discord: discordBot ? 'online' : 'offline',
            ai: chatbot.aiService?.getStatus()?.provider || 'fallback'
          }
        });
      });

      // Serve static files for web interface
      const webPath = path.join(process.cwd(), 'web');
      const webDistPath = path.join(webPath, 'dist');

      // Check for built version first, then fallback to development version
      const hasDistPath = fs.pathExistsSync(webDistPath);
      const hasWebPath = fs.pathExistsSync(webPath);

      if (hasDistPath) {
        app.use(express.static(webDistPath));
        app.get('*', (req, res) => {
          if (!req.path.startsWith('/api')) {
            res.sendFile(path.join(webDistPath, 'index.html'));
          }
        });
      } else if (hasWebPath) {
        app.use(express.static(webPath));
        app.get('*', (req, res) => {
          if (!req.path.startsWith('/api')) {
            res.sendFile(path.join(webPath, 'index.html'));
          }
        });
      }

      // Error handling middleware
      app.use((err, req, res, next) => {
        console.error('Web Server Error:', err);
        res.status(500).json({
          error: 'Internal server error',
          message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
        });
      });

      // Start server
      const serverConfig = ConfigManager.getServerConfig();
      const PORT = serverConfig.port;
      const HOST = serverConfig.host;

      const server = app.listen(PORT, HOST, () => {
        console.log(`✅ Web Server running on http://${HOST}:${PORT}`);
        console.log(`   📊 Health check: http://${HOST}:${PORT}/health`);
        console.log(`   🌐 API endpoint: http://${HOST}:${PORT}/api`);
        console.log(`   🎨 Web interface: http://${HOST}:${PORT}`);
        
        // Store server reference for graceful shutdown
        global.webServer = server;
        resolve();
      });

      server.on('error', (error) => {
        console.error('❌ Web Server failed to start:', error);
        reject(error);
      });

    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Start Discord Bot
 */
async function startDiscordBot(config, sharedChatbot = null) {
  try {
    // Create Discord bot instance
    const bot = new DiscordBot(config);

    // If we have a shared chatbot, use it instead of creating a new one
    if (sharedChatbot) {
      await bot.initialize();
      bot.chatbot = sharedChatbot;
    }

    // Start the bot
    await bot.start();

    console.log('✅ Discord Bot started successfully');

    // Store bot reference for graceful shutdown
    global.discordBot = bot;

    return bot;

  } catch (error) {
    console.error('❌ Discord Bot failed to start:', error.message);
    console.log('   🔄 Web server will continue running without Discord bot');
    return null;
  }
}

/**
 * Setup graceful shutdown
 */
function setupGracefulShutdown() {
  const shutdown = async (signal) => {
    console.log(`\n🔄 Received ${signal}, shutting down gracefully...`);
    
    try {
      // Stop Discord bot
      if (global.discordBot) {
        console.log('🤖 Stopping Discord bot...');
        await global.discordBot.stop();
      }
      
      // Stop web server
      if (global.webServer) {
        console.log('🌐 Stopping web server...');
        global.webServer.close();
      }
      
      console.log('✅ Kora 2.0 shut down successfully');
      process.exit(0);
      
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
  
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    shutdown('UNCAUGHT_EXCEPTION');
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    shutdown('UNHANDLED_REJECTION');
  });
}

// Clean up old sessions periodically
setInterval(() => {
  // This will be handled by the shared chatbot instance
}, 60 * 60 * 1000); // Every hour

// Start Kora 2.0
startKora();
