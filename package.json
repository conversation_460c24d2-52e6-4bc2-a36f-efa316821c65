{"name": "kora-2.0", "version": "1.0.0", "description": "Advanced AI chatbot with personality-driven conversations for Discord and Web platforms", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "web": "cd web && npm run dev", "build": "cd web && npm run build", "test": "jest", "discord": "node src/discord-bot.js"}, "keywords": ["chatbot", "ai", "discord", "personality", "conversation"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "discord.js": "^14.20.0", "dotenv": "^16.5.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10"}}