{"name": "kora-2.0", "version": "1.0.0", "description": "Advanced AI chatbot with personality-driven conversations for Discord and Web platforms", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/start-all.js", "start:web": "node src/index.js", "start:discord": "node src/discord-bot.js", "start:both": "concurrently \"npm run start:web\" \"npm run start:discord\"", "dev": "nodemon src/start-all.js", "dev:web": "nodemon src/index.js", "dev:both": "concurrently \"npm run dev:web\" \"nodemon src/discord-bot.js\"", "web": "cd web && npm run dev", "build": "cd web && npm run build", "test": "jest --verbose", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "discord": "node src/discord-bot.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js"}, "keywords": ["chatbot", "ai", "discord", "personality", "conversation"], "author": "", "license": "ISC", "jest": {"type": "module", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!src/discord-bot.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "discord.js": "^14.20.0", "dotenv": "^16.5.0", "express": "^4.21.2", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "concurrently": "^9.1.2", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}