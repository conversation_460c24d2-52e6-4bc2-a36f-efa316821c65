# 🚀 Como Iniciar Kora 2.0 - <PERSON><PERSON> as Opções

## 🎯 **Opção 1: Arquivo Único (RECOMENDADO)**

### ✅ **Mais Simples - Um comando só**
```bash
npm start
```

**O que faz:**
- ✅ Inicia servidor web na porta 3000
- ✅ Inicia Discord bot automaticamente (se configurado)
- ✅ Usa o mesmo chatbot engine para ambos
- ✅ Logs organizados e coloridos
- ✅ Shutdown graceful com Ctrl+C

**Vantagens:**
- 🎯 **Mais fácil**: Um comando só
- 🔄 **Compartilhado**: Mesma instância do chatbot
- 📊 **Logs unificados**: Tudo no mesmo terminal
- ⚡ **Mais eficiente**: Menos uso de memória

---

## 🎯 **Opção 2: Concurrently (Processos Separados)**

### ✅ **Para desenvolvimento com logs separados**
```bash
npm run start:both
```

**Desenvolvimento com auto-reload:**
```bash
npm run dev:both
```

**O que faz:**
- ✅ Inicia web server em um processo
- ✅ Inicia Discord bot em outro processo
- ✅ Logs coloridos e separados por serviço
- ✅ Mata ambos com Ctrl+C

**Vantagens:**
- 🔍 **Logs separados**: Fácil debug
- 🔄 **Auto-reload**: Para desenvolvimento
- 🎨 **Cores diferentes**: Web (azul) e Discord (verde)

---

## 🎯 **Opção 3: PM2 (Produção)**

### ✅ **Para produção com monitoramento**

**Instalar PM2:**
```bash
npm install -g pm2
```

**Iniciar:**
```bash
npm run pm2:start
```

**Monitorar:**
```bash
pm2 monit
pm2 logs
pm2 status
```

**Parar:**
```bash
npm run pm2:stop
```

**Vantagens:**
- 🏭 **Produção**: Auto-restart, logs, monitoramento
- 📊 **Dashboard**: Interface web de monitoramento
- 🔄 **Auto-restart**: Se crashar, reinicia automaticamente
- 📝 **Logs persistentes**: Salvos em arquivos

---

## 🎯 **Opção 4: Manual (Controle Total)**

### ✅ **Para controle individual**

**Terminal 1 - Web Server:**
```bash
npm run start:web
```

**Terminal 2 - Discord Bot:**
```bash
npm run start:discord
```

**Vantagens:**
- 🎛️ **Controle total**: Liga/desliga cada serviço
- 🔍 **Debug individual**: Foco em um serviço
- 🔄 **Restart individual**: Reinicia só o que precisa

---

## 📋 **Resumo dos Comandos**

| Comando | Descrição | Uso |
|---------|-----------|-----|
| `npm start` | **🏆 RECOMENDADO** - Tudo junto | Produção/Uso normal |
| `npm run start:both` | Processos separados | Desenvolvimento |
| `npm run dev` | Auto-reload tudo junto | Desenvolvimento |
| `npm run dev:both` | Auto-reload separado | Desenvolvimento |
| `npm run pm2:start` | PM2 produção | Servidor/Produção |
| `npm run start:web` | Só web server | Teste/Debug |
| `npm run start:discord` | Só Discord bot | Teste/Debug |

---

## 🔧 **Configuração Necessária**

### **Arquivo `.env` deve ter:**
```env
# GitHub Models API
GITHUB_TOKEN=seu_token_github_aqui

# Discord Bot
DISCORD_TOKEN=seu_token_discord_aqui
DISCORD_CLIENT_ID=seu_client_id_aqui

# Servidor
PORT=3000
NODE_ENV=development
```

### **Para habilitar Discord:**
No arquivo `config/development.json`:
```json
{
  "discord": {
    "enabled": true
  }
}
```

---

## 🎯 **Qual Usar?**

### 🏆 **Para uso normal:**
```bash
npm start
```

### 🔧 **Para desenvolvimento:**
```bash
npm run dev
```

### 🏭 **Para produção:**
```bash
npm run pm2:start
```

### 🔍 **Para debug:**
```bash
npm run start:web    # Terminal 1
npm run start:discord # Terminal 2
```

---

## 📊 **Verificar se está funcionando:**

### **1. Health Check:**
```bash
curl http://localhost:3000/health
```

### **2. AI Status:**
```bash
curl http://localhost:3000/api/ai/status
```

### **3. Chat Test:**
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Olá!"}'
```

### **4. Discord Test:**
- Mencione o bot no Discord: `@Kora olá!`

---

## 🚨 **Solução de Problemas**

### **Se o Discord não conectar:**
1. Verifique `DISCORD_TOKEN` no `.env`
2. Verifique se Discord está `"enabled": true`
3. Teste só o Discord: `npm run start:discord`

### **Se a AI não funcionar:**
1. Verifique `GITHUB_TOKEN` no `.env`
2. Teste: `curl http://localhost:3000/api/ai/status`
3. Deve mostrar `"hasGitHubToken": true`

### **Se nada funcionar:**
1. Verifique se `.env` existe
2. Verifique se as dependências estão instaladas: `npm install`
3. Teste só o web: `npm run start:web`

---

## 🎉 **Resultado Esperado**

Quando tudo estiver funcionando, você verá:

```
🚀 Starting Kora 2.0 - AI Chatbot System...

🔧 Configuration Summary: {...}

🤖 Initializing Chatbot Engine...
✅ AI Service connected: github (gpt-4o)
✅ Chatbot Engine initialized successfully

🌐 Starting Web Server...
✅ Web Server running on http://localhost:3000

🤖 Starting Discord Bot...
✅ Discord Bot started successfully

🎉 Kora 2.0 is fully operational!
📋 Services running:
   🌐 Web Server: http://localhost:3000
   🤖 Discord Bot: Online
   🧠 AI Provider: github
```

**Agora é só usar!** 🚀
