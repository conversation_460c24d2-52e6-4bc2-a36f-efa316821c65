import fs from 'fs-extra';
import path from 'path';

/**
 * Command Processor - Handles special commands and system operations
 */
class CommandProcessor {
  constructor(chatbotEngine, config = {}) {
    this.chatbot = chatbotEngine;
    this.config = config;
    this.commandPrefix = config.prefix || '/';
    this.enabledCommands = config.availableCommands || [
      'set_personality',
      'load_memory',
      'reset_context',
      'help',
      'status',
      'save_memory',
      'load_lore',
      'export_data',
      'import_data',
      'stats'
    ];
  }

  /**
   * Check if a message is a command
   */
  isCommand(message) {
    return message.startsWith(this.commandPrefix);
  }

  /**
   * Parse and execute a command
   */
  async processCommand(message, session) {
    if (!this.isCommand(message)) {
      return null;
    }

    const commandLine = message.slice(this.commandPrefix.length);
    const [command, ...args] = commandLine.split(' ');
    const commandLower = command.toLowerCase();

    // Check if command is enabled
    if (!this.enabledCommands.includes(commandLower)) {
      return {
        content: `Command "${command}" is not available. Type ${this.commandPrefix}help for available commands.`,
        isCommand: true,
        error: true
      };
    }

    try {
      switch (commandLower) {
        case 'set_personality':
          return await this.handleSetPersonality(args, session);
        case 'load_memory':
          return await this.handleLoadMemory(args, session);
        case 'reset_context':
          return await this.handleResetContext(session);
        case 'help':
          return this.handleHelp(args);
        case 'status':
          return this.handleStatus(session);
        case 'save_memory':
          return await this.handleSaveMemory(args, session);
        case 'load_lore':
          return await this.handleLoadLore(args, session);
        case 'export_data':
          return await this.handleExportData(args, session);
        case 'import_data':
          return await this.handleImportData(args, session);
        case 'stats':
          return this.handleStats(session);
        default:
          return {
            content: `Unknown command: ${command}. Type ${this.commandPrefix}help for available commands.`,
            isCommand: true,
            error: true
          };
      }
    } catch (error) {
      console.error(`Error executing command ${command}:`, error);
      return {
        content: `Error executing command: ${error.message}`,
        isCommand: true,
        error: true
      };
    }
  }

  /**
   * Handle personality change command
   */
  async handleSetPersonality(args, session) {
    if (args.length === 0) {
      const available = Object.keys(this.chatbot.personalities).join(', ');
      return {
        content: `Usage: ${this.commandPrefix}set_personality <personality>\nAvailable personalities: ${available}`,
        isCommand: true
      };
    }

    const personalityName = args[0].toLowerCase();
    if (!this.chatbot.personalities[personalityName]) {
      const available = Object.keys(this.chatbot.personalities).join(', ');
      return {
        content: `Personality "${personalityName}" not found.\nAvailable personalities: ${available}`,
        isCommand: true,
        error: true
      };
    }

    session.personality = personalityName;
    const personality = this.chatbot.personalities[personalityName];
    
    return {
      content: `✅ Personality changed to "${personality.name}"\n📝 ${personality.description}\n🎭 Traits: ${personality.traits.join(', ')}`,
      isCommand: true
    };
  }

  /**
   * Handle memory loading command
   */
  async handleLoadMemory(args, session) {
    if (args.length === 0) {
      return {
        content: `Usage: ${this.commandPrefix}load_memory <filename>\nLoads memory entries from a JSON file in the data directory.`,
        isCommand: true
      };
    }

    const filename = args[0];
    const filePath = path.join(process.cwd(), 'data', filename);

    try {
      const success = await this.chatbot.memoryManager.importMemoryFromFile(filePath);
      if (success) {
        return {
          content: `✅ Memory loaded successfully from ${filename}`,
          isCommand: true
        };
      } else {
        return {
          content: `❌ Failed to load memory from ${filename}`,
          isCommand: true,
          error: true
        };
      }
    } catch (error) {
      return {
        content: `❌ Error loading memory: ${error.message}`,
        isCommand: true,
        error: true
      };
    }
  }

  /**
   * Handle context reset command
   */
  async handleResetContext(session) {
    const messageCount = session.context.length;
    session.context = [];
    
    return {
      content: `✅ Context reset successfully. Cleared ${messageCount} messages.\n🆕 Starting fresh conversation!`,
      isCommand: true
    };
  }

  /**
   * Handle help command
   */
  handleHelp(args) {
    if (args.length > 0) {
      // Specific command help
      const command = args[0].toLowerCase();
      return this.getCommandHelp(command);
    }

    // General help
    const commandList = this.enabledCommands.map(cmd => `${this.commandPrefix}${cmd}`).join('\n• ');
    
    return {
      content: `🤖 **Kora 2.0 Command Help**

**Available Commands:**
• ${commandList}

**Personality System:**
I can adapt my personality to match your preferences. Available personalities: ${Object.keys(this.chatbot.personalities).join(', ')}

**Memory & Context:**
I remember our conversations and can learn from imported data files.

**Usage Tips:**
• Just talk naturally - I'll adapt to your mood and style
• Use commands to customize my behavior
• I maintain context throughout our conversation

Type \`${this.commandPrefix}help <command>\` for specific command help.`,
      isCommand: true
    };
  }

  /**
   * Get help for a specific command
   */
  getCommandHelp(command) {
    const helpTexts = {
      set_personality: `**${this.commandPrefix}set_personality <name>**
Change my personality and conversation style.

Available personalities: ${Object.keys(this.chatbot.personalities).join(', ')}

Example: \`${this.commandPrefix}set_personality flirty\``,

      load_memory: `**${this.commandPrefix}load_memory <filename>**
Load memory entries from a JSON file in the data directory.

The file should contain an array of strings or objects with content.

Example: \`${this.commandPrefix}load_memory user_memories.json\``,

      reset_context: `**${this.commandPrefix}reset_context**
Clear the current conversation context and start fresh.

This removes all previous messages from the current session but keeps your personality settings.`,

      status: `**${this.commandPrefix}status**
Show current session information including personality, context length, and memory stats.`,

      save_memory: `**${this.commandPrefix}save_memory <text>**
Save important information to my long-term memory.

Example: \`${this.commandPrefix}save_memory My favorite color is blue\``,

      load_lore: `**${this.commandPrefix}load_lore <filename>**
Load lore/world-building data from a JSON file.

Example: \`${this.commandPrefix}load_lore fantasy_world.json\``,

      export_data: `**${this.commandPrefix}export_data [memory|lore]**
Export memory or lore data to a file.

Example: \`${this.commandPrefix}export_data memory\``,

      stats: `**${this.commandPrefix}stats**
Show detailed statistics about memory, lore, and conversation data.`
    };

    return {
      content: helpTexts[command] || `No help available for command: ${command}`,
      isCommand: true
    };
  }

  /**
   * Handle status command
   */
  handleStatus(session) {
    const personality = this.chatbot.personalities[session.personality];
    const contextStats = this.chatbot.contextManager.getContextStats(session);
    const memoryStats = this.chatbot.memoryManager.getMemoryStats();
    const moodAnalysis = this.chatbot.contextManager.analyzeMood(session);

    return {
      content: `📊 **Session Status**

**Current Settings:**
• Personality: ${personality.name}
• Session ID: ${session.id}
• Platform: Web/Discord

**Conversation Stats:**
• Context Messages: ${contextStats.totalMessages}
• User Messages: ${contextStats.userMessages}
• Bot Messages: ${contextStats.botMessages}
• Important Messages: ${contextStats.importantMessages}

**Memory & Data:**
• Memory Entries: ${memoryStats.totalEntries}
• Lore Entries: ${this.chatbot.memoryManager.lore.size}

**Current Mood:** ${moodAnalysis.mood} (${Math.round(moodAnalysis.confidence * 100)}% confidence)

**Session Duration:** ${contextStats.duration ? Math.round(contextStats.duration / 1000 / 60) + ' minutes' : 'Just started'}`,
      isCommand: true
    };
  }

  /**
   * Handle save memory command
   */
  async handleSaveMemory(args, session) {
    if (args.length === 0) {
      return {
        content: `Usage: ${this.commandPrefix}save_memory <text>\nSave important information to long-term memory.`,
        isCommand: true
      };
    }

    const memoryText = args.join(' ');
    const entry = this.chatbot.addMemory(memoryText, 'user_command', {
      sessionId: session.id,
      importance: 2
    });

    return {
      content: `✅ Memory saved: "${memoryText}"\n🆔 Entry ID: ${entry.id.substr(0, 8)}`,
      isCommand: true
    };
  }

  /**
   * Handle load lore command
   */
  async handleLoadLore(args, session) {
    if (args.length === 0) {
      return {
        content: `Usage: ${this.commandPrefix}load_lore <filename>\nLoad lore data from a JSON file in the data directory.`,
        isCommand: true
      };
    }

    const filename = args[0];
    const filePath = path.join(process.cwd(), 'data', filename);

    try {
      const success = await this.chatbot.memoryManager.importLoreFromFile(filePath);
      if (success) {
        return {
          content: `✅ Lore loaded successfully from ${filename}`,
          isCommand: true
        };
      } else {
        return {
          content: `❌ Failed to load lore from ${filename}`,
          isCommand: true,
          error: true
        };
      }
    } catch (error) {
      return {
        content: `❌ Error loading lore: ${error.message}`,
        isCommand: true,
        error: true
      };
    }
  }

  /**
   * Handle export data command
   */
  async handleExportData(args, session) {
    const dataType = args[0] || 'memory';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    try {
      if (dataType === 'memory') {
        const filename = `memory_export_${timestamp}.json`;
        const filePath = path.join(process.cwd(), 'data', 'exports', filename);
        await fs.ensureDir(path.dirname(filePath));
        
        const data = await this.chatbot.memoryManager.exportMemory(filePath);
        return {
          content: `✅ Memory exported to ${filename}\n📊 ${data.totalEntries} entries exported`,
          isCommand: true
        };
      } else if (dataType === 'lore') {
        const filename = `lore_export_${timestamp}.json`;
        const filePath = path.join(process.cwd(), 'data', 'exports', filename);
        await fs.ensureDir(path.dirname(filePath));
        
        const data = await this.chatbot.memoryManager.exportLore(filePath);
        return {
          content: `✅ Lore exported to ${filename}\n📊 ${data.totalEntries} entries exported`,
          isCommand: true
        };
      } else {
        return {
          content: `Usage: ${this.commandPrefix}export_data [memory|lore]\nExport data to a JSON file.`,
          isCommand: true
        };
      }
    } catch (error) {
      return {
        content: `❌ Export failed: ${error.message}`,
        isCommand: true,
        error: true
      };
    }
  }

  /**
   * Handle import data command
   */
  async handleImportData(args, session) {
    if (args.length === 0) {
      return {
        content: `Usage: ${this.commandPrefix}import_data <filename>\nImport data from a JSON file in the data directory.`,
        isCommand: true
      };
    }

    const filename = args[0];
    const filePath = path.join(process.cwd(), 'data', filename);

    try {
      // Try to import as memory first, then as lore
      const memorySuccess = await this.chatbot.memoryManager.importMemoryFromFile(filePath);
      const loreSuccess = await this.chatbot.memoryManager.importLoreFromFile(filePath);

      if (memorySuccess || loreSuccess) {
        return {
          content: `✅ Data imported successfully from ${filename}`,
          isCommand: true
        };
      } else {
        return {
          content: `❌ Failed to import data from ${filename}`,
          isCommand: true,
          error: true
        };
      }
    } catch (error) {
      return {
        content: `❌ Import failed: ${error.message}`,
        isCommand: true,
        error: true
      };
    }
  }

  /**
   * Handle stats command
   */
  handleStats(session) {
    const memoryStats = this.chatbot.memoryManager.getMemoryStats();
    const loreStats = this.chatbot.memoryManager.getLoreStats();
    const contextStats = this.chatbot.contextManager.getContextStats(session);

    return {
      content: `📈 **Detailed Statistics**

**Memory System:**
• Total Entries: ${memoryStats.totalEntries}
• Categories: ${Object.keys(memoryStats.categoryDistribution).join(', ')}
• Types: ${Object.keys(memoryStats.typeDistribution).join(', ')}

**Lore System:**
• Total Entries: ${loreStats.totalEntries}
• Categories: ${Object.keys(loreStats.categoryDistribution).join(', ')}
• Total Accesses: ${loreStats.totalAccesses}
• Average Accesses: ${loreStats.averageAccesses.toFixed(2)}

**Current Session:**
• Total Messages: ${contextStats.totalMessages}
• Platforms Used: ${Object.keys(contextStats.platforms).join(', ')}
• Session Duration: ${contextStats.duration ? Math.round(contextStats.duration / 1000 / 60) + ' minutes' : 'Just started'}

**System:**
• Active Sessions: ${this.chatbot.sessions.size}
• Available Personalities: ${Object.keys(this.chatbot.personalities).length}`,
      isCommand: true
    };
  }
}

export default CommandProcessor;
