import express from 'express';
import cors from 'cors';
import ChatbotEngine from './core/ChatbotEngine.js';
import { createWebAPI } from './web-api/routes.js';
import ConfigManager from './utils/ConfigManager.js';

/**
 * Simple Kora 2.0 Server
 */
async function startServer() {
  console.log('🚀 Starting Kora 2.0 Simple Server...');

  try {
    // Load configuration
    const config = ConfigManager.getAll();

    // Initialize the chatbot engine
    const chatbotConfig = {
      personalities: config.personalities || {},
      defaultPersonality: config.chatbot?.defaultPersonality || config.defaultPersonality || 'friendly',
      contextWindow: config.chatbot?.contextWindow || config.contextWindow || 10,
      maxMemoryEntries: config.chatbot?.maxMemoryEntries || config.maxMemoryEntries || 100,
      commands: config.commands || {},
      ai: config.chatbot?.ai || config.ai || {}
    };

    console.log('🤖 Initializing Chatbot Engine...');
    const chatbot = new ChatbotEngine(chatbotConfig);

    // Wait for initialization
    await new Promise((resolve, reject) => {
      chatbot.once('initialized', resolve);
      chatbot.once('error', reject);
      setTimeout(() => reject(new Error('Timeout')), 10000);
    });

    console.log('✅ Chatbot Engine initialized');

    // Create Express app
    const app = express();

    // Basic middleware
    app.use(cors());
    app.use(express.json());

    // API Routes
    app.use('/api', createWebAPI(chatbot, chatbotConfig));

    // Health check endpoint
    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    });

    // Start server
    const PORT = 3000;
    const HOST = 'localhost';

    app.listen(PORT, HOST, () => {
      console.log(`✅ Kora 2.0 Simple Server running on http://${HOST}:${PORT}`);
      console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
      console.log(`🌐 API endpoint: http://${HOST}:${PORT}/api`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();
