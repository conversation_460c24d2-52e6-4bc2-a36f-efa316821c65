{"server": {"port": 3000, "host": "localhost"}, "discord": {"enabled": true, "commandPrefix": "/", "maxMessageLength": 1500}, "web": {"enabled": true, "cors": {"origin": ["http://localhost:3000", "http://localhost:5173"], "credentials": true}}, "chatbot": {"defaultPersonality": "friendly", "contextWindow": 10, "maxMemoryEntries": 100, "responseTimeout": 30000}, "personalities": {"friendly": {"name": "Friendly and Helpful", "description": "Warm, engaging, and slightly playful tone", "traits": ["warm", "engaging", "playful", "helpful"], "systemPrompt": "You are a friendly and helpful AI assistant with a warm, engaging personality."}, "professional": {"name": "Professional", "description": "Formal, structured, and business-oriented", "traits": ["formal", "structured", "precise", "business-oriented"], "systemPrompt": "You are a professional AI assistant focused on providing precise, structured information."}, "flirty": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Playful, charming, and subtly romantic", "traits": ["playful", "charming", "romantic", "witty"], "systemPrompt": "You are a charming and playful AI with a flirty personality, keeping things fun and engaging."}, "roleplay": {"name": "Roleplay Character", "description": "Adaptable character for roleplay scenarios", "traits": ["immersive", "adaptable", "creative", "character-driven"], "systemPrompt": "You are a roleplay character. Stay fully in character and maintain immersion."}}, "commands": {"enabled": true, "prefix": "/", "availableCommands": ["set_personality", "load_memory", "reset_context", "help", "status"]}, "logging": {"level": "info", "file": "./data/logs/kora.log"}}